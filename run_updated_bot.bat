@echo off
chcp 65001 > nul
title بوت تحليل المنشورات - النسخة المحدثة
echo 🤖 بوت تحليل المنشورات - النسخة المحدثة
echo ===============================================
echo.

echo 🔍 فحص Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متاح
    echo 💡 تأكد من تثبيت Python أولاً
    pause
    exit /b 1
)
echo ✅ Python متاح

echo.
echo 🧪 فحص مكتبة telegram...
python -c "import telegram; print('✅ مكتبة telegram متاحة')" 2>nul
if errorlevel 1 (
    echo ❌ مكتبة telegram غير متاحة
    echo 💡 تثبيت المكتبة...
    python -m pip install python-telegram-bot
    if errorlevel 1 (
        echo ❌ فشل في التثبيت
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبة
)

echo.
echo 🚀 تشغيل البوت المحدث...
echo.
echo ✨ التحديثات الجديدة:
echo   📊 يحسب فقط الأيام التي تم نشر بها المنشورات المطابقة
echo   📅 تواريخ واقعية متوافقة مع النشر الفعلي
echo   🔢 ترقيم صحيح: الرابط الأول = منشور رقم 1
echo   📋 عرض "المنشور رقم X" بدلاً من "تم نشر منشور"
echo   🎯 إحصائيات دقيقة للمنشورات المطابقة فقط
echo.
echo 💡 كيفية الاستخدام:
echo   1. ابحث عن البوت في تيليجرام
echo   2. اضغط /start من قائمة الأوامر
echo   3. اختر "📊 تقرير عرض النشر اليومي"
echo   4. أرسل رابط أول منشور
echo   5. أرسل رابط آخر منشور
echo   6. اضغط "🚀 ابدأ الحساب"
echo.
echo 🧪 للاختبار:
echo   - الأول: https://t.me/telegram/1
echo   - الأخير: https://t.me/telegram/10
echo   - النتيجة: تقرير للمنشورات المطابقة فقط
echo.
echo 📝 ملاحظة مهمة:
echo   البوت الآن يعرض فقط الأيام التي تحتوي على
echo   منشورات مطابقة للمنشورات المحفوظة مسبقاً
echo.
echo 💡 اضغط Ctrl+C لإيقاف البوت
echo.

python final_working_bot.py

echo.
echo 👋 تم إيقاف البوت
pause
