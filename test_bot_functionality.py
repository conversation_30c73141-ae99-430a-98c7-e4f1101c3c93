#!/usr/bin/env python3
"""
اختبار شامل لوظائف البوت
"""

import re
import asyncio

def test_link_extraction():
    """اختبار استخراج الروابط"""
    print("🔗 اختبار استخراج الروابط...")
    
    def extract_telegram_link(url: str):
        patterns = [
            r'(?:https?://)?(?:www\.)?t\.me/([^/\s]+)/(\d+)',
            r'(?:https?://)?(?:www\.)?telegram\.me/([^/\s]+)/(\d+)',
            r'(?:https?://)?(?:www\.)?telegram\.org/([^/\s]+)/(\d+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url.strip())
            if match:
                return {
                    'channel': match.group(1),
                    'message_id': int(match.group(2)),
                    'url': url.strip()
                }
        return None
    
    # اختبار روابط مختلفة
    test_urls = [
        "https://t.me/telegram/1",
        "t.me/durov/123",
        "https://telegram.me/channel/456",
        "telegram.org/news/789",
        "https://t.me/test_channel/999",
        "t.me/arabic_channel/555"
    ]
    
    success = True
    for url in test_urls:
        result = extract_telegram_link(url)
        if result:
            print(f"✅ {url} -> @{result['channel']}/{result['message_id']}")
        else:
            print(f"❌ فشل: {url}")
            success = False
    
    return success

def test_count_calculation():
    """اختبار حساب عدد المنشورات"""
    print("\n🔢 اختبار حساب العدد...")
    
    test_cases = [
        (1, 10, 10),      # 1 إلى 10 = 10 منشورات
        (5, 15, 11),     # 5 إلى 15 = 11 منشور
        (100, 200, 101), # 100 إلى 200 = 101 منشور
        (10, 5, 6),      # 10 إلى 5 = 6 منشورات (عكسي)
        (50, 50, 1),     # نفس الرقم = منشور واحد
    ]
    
    success = True
    for first, last, expected in test_cases:
        start_id = min(first, last)
        end_id = max(first, last)
        total = end_id - start_id + 1
        
        if total == expected:
            print(f"✅ من {first} إلى {last} = {total} منشور")
        else:
            print(f"❌ من {first} إلى {last} = {total} منشور (متوقع: {expected})")
            success = False
    
    return success

def test_user_flow_simulation():
    """محاكاة تدفق المستخدم"""
    print("\n👤 محاكاة تدفق المستخدم...")
    
    # محاكاة قاموس المستخدمين
    users = {}
    user_id = 12345
    
    # الخطوة 1: بدء البوت
    users[user_id] = {
        "step": "main_menu",
        "first_url": None,
        "last_url": None,
        "first_info": None,
        "last_info": None
    }
    print("✅ 1. بدء البوت")
    
    # الخطوة 2: اختيار تقرير النشر
    users[user_id]["step"] = "waiting_first_url"
    print("✅ 2. اختيار تقرير النشر اليومي")
    
    # الخطوة 3: إدخال الرابط الأول
    first_url = "https://t.me/telegram/1"
    
    def extract_telegram_link(url):
        match = re.search(r't\.me/([^/\s]+)/(\d+)', url)
        if match:
            return {
                'channel': match.group(1),
                'message_id': int(match.group(2)),
                'url': url
            }
        return None
    
    first_info = extract_telegram_link(first_url)
    if first_info:
        users[user_id]["first_url"] = first_url
        users[user_id]["first_info"] = first_info
        users[user_id]["step"] = "waiting_last_url"
        print(f"✅ 3. حفظ الرابط الأول: @{first_info['channel']}/{first_info['message_id']}")
    else:
        print("❌ 3. فشل في حفظ الرابط الأول")
        return False
    
    # الخطوة 4: إدخال الرابط الأخير
    last_url = "https://t.me/telegram/10"
    last_info = extract_telegram_link(last_url)
    
    if last_info and last_info['channel'] == first_info['channel']:
        users[user_id]["last_url"] = last_url
        users[user_id]["last_info"] = last_info
        users[user_id]["step"] = "ready_to_calculate"
        
        # حساب العدد
        first_id = first_info['message_id']
        last_id = last_info['message_id']
        start_id = min(first_id, last_id)
        end_id = max(first_id, last_id)
        total_posts = end_id - start_id + 1
        
        print(f"✅ 4. حفظ الرابط الأخير: إجمالي {total_posts} منشور")
    else:
        print("❌ 4. فشل في حفظ الرابط الأخير")
        return False
    
    # الخطوة 5: جاهز للحساب
    if users[user_id]["step"] == "ready_to_calculate":
        print("✅ 5. جاهز لبدء التحليل")
        return True
    
    return False

async def test_telegram_import():
    """اختبار استيراد مكتبة telegram"""
    print("\n📚 اختبار مكتبة telegram...")
    
    try:
        from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, BotCommand
        from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes
        print("✅ مكتبة python-telegram-bot متاحة")
        return True
    except ImportError as e:
        print(f"❌ مكتبة python-telegram-bot غير متاحة: {e}")
        print("💡 قم بتثبيتها: python -m pip install python-telegram-bot")
        return False

async def test_bot_creation():
    """اختبار إنشاء البوت"""
    print("\n🤖 اختبار إنشاء البوت...")
    
    try:
        from telegram.ext import Application
        BOT_TOKEN = "8415486238:AAFpoUZq_OXzZh74o8GrHmXXI7LwqPikSxI"
        
        app = Application.builder().token(BOT_TOKEN).build()
        print("✅ تم إنشاء تطبيق البوت")
        
        # اختبار الاتصال
        bot = app.bot
        me = await bot.get_me()
        print(f"✅ معلومات البوت: {me.first_name} (@{me.username})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البوت: {e}")
        return False

def test_error_handling():
    """اختبار معالجة الأخطاء"""
    print("\n⚠️ اختبار معالجة الأخطاء...")
    
    def extract_telegram_link(url):
        patterns = [r't\.me/([^/\s]+)/(\d+)']
        for pattern in patterns:
            match = re.search(pattern, url.strip())
            if match:
                return {'channel': match.group(1), 'message_id': int(match.group(2))}
        return None
    
    # اختبار روابط خاطئة
    bad_urls = [
        "https://google.com",
        "not a url",
        "https://t.me/channel",  # بدون رقم منشور
        "t.me/",
        "",
        "https://t.me/channel/abc"  # رقم منشور غير صحيح
    ]
    
    success = True
    for url in bad_urls:
        try:
            result = extract_telegram_link(url)
            if result is None:
                print(f"✅ رفض رابط خاطئ: {url}")
            else:
                print(f"❌ قبل رابط خاطئ: {url}")
                success = False
        except Exception as e:
            print(f"❌ خطأ في معالجة: {url} - {e}")
            success = False
    
    return success

async def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 اختبار شامل لوظائف البوت")
    print("=" * 50)
    
    tests = [
        ("استخراج الروابط", test_link_extraction()),
        ("حساب العدد", test_count_calculation()),
        ("تدفق المستخدم", test_user_flow_simulation()),
        ("مكتبة telegram", await test_telegram_import()),
        ("إنشاء البوت", await test_bot_creation()),
        ("معالجة الأخطاء", test_error_handling())
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, result in tests:
        if result:
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n📋 البوت جاهز للاستخدام:")
        print("1. شغل: python tested_bot.py")
        print("2. ابحث عن البوت في تيليجرام")
        print("3. أرسل /start")
        print("4. جرب الروابط:")
        print("   - الأول: https://t.me/telegram/1")
        print("   - الأخير: https://t.me/telegram/10")
        print("   - النتيجة: 10 منشورات")
        
        print("\n✨ مميزات البوت:")
        print("- ✅ يقرأ الروابط بشكل صحيح")
        print("- ✅ يحسب العدد تلقائياً")
        print("- ✅ يتحقق من القناة")
        print("- ✅ قائمة أوامر في الشات")
        print("- ✅ رسائل واضحة")
        print("- ✅ معالجة أخطاء جيدة")
        
    else:
        print("❌ بعض الاختبارات فشلت")
        print("💡 تحقق من المتطلبات وأعد المحاولة")
    
    return passed == total

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if result:
            print("\n🚀 البوت مختبر وجاهز للاستخدام!")
        else:
            print("\n🔧 يحتاج إصلاحات قبل الاستخدام")
    except Exception as e:
        print(f"\n💥 خطأ في الاختبار: {e}")
    
    input("\nاضغط Enter للخروج...")
