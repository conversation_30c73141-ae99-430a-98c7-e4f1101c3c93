#!/usr/bin/env python3
"""
قارئ المنشورات من تيليجرام باستخدام Pyrogram
"""

import asyncio
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from pyrogram import Client
from pyrogram.types import Message
from pyrogram.errors import FloodWait, ChannelPrivate, UsernameNotOccupied

from config import API_ID, API_HASH, BOT_TOKEN

class TelegramReader:
    def __init__(self):
        """تهيئة قارئ تيليجرام"""
        self.client = None
        
    async def initialize(self):
        """تهيئة العميل"""
        try:
            self.client = Client(
                "bot_session",
                api_id=API_ID,
                api_hash=API_HASH,
                bot_token=BOT_TOKEN
            )
            await self.client.start()
            print("✅ تم تهيئة قارئ تيليجرام بنجاح")
            return True
        except Exception as e:
            print(f"❌ خطأ في تهيئة قارئ تيليجرام: {e}")
            return False
    
    async def close(self):
        """إغلاق العميل"""
        if self.client:
            await self.client.stop()
    
    def extract_channel_info(self, url: str) -> Optional[Dict]:
        """استخراج معلومات القناة من الرابط"""
        # أنماط الروابط المدعومة
        patterns = [
            r't\.me/([^/]+)/(\d+)',  # t.me/channel/123
            r'telegram\.me/([^/]+)/(\d+)',  # telegram.me/channel/123
            r'telegram\.org/([^/]+)/(\d+)',  # telegram.org/channel/123
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                channel_username = match.group(1)
                message_id = int(match.group(2))
                return {
                    'channel': channel_username,
                    'message_id': message_id,
                    'url': url
                }
        
        return None
    
    async def get_message(self, channel: str, message_id: int) -> Optional[Message]:
        """جلب رسالة محددة من قناة"""
        try:
            message = await self.client.get_messages(channel, message_id)
            return message
        except ChannelPrivate:
            print(f"❌ القناة {channel} خاصة أو غير متاحة")
            return None
        except UsernameNotOccupied:
            print(f"❌ القناة {channel} غير موجودة")
            return None
        except FloodWait as e:
            print(f"⏳ انتظار {e.value} ثانية بسبب حد المعدل...")
            await asyncio.sleep(e.value)
            return await self.get_message(channel, message_id)
        except Exception as e:
            print(f"❌ خطأ في جلب الرسالة {message_id} من {channel}: {e}")
            return None
    
    async def get_messages_range(self, channel: str, start_id: int, end_id: int) -> List[Message]:
        """جلب مجموعة من الرسائل بين معرفين"""
        messages = []
        
        try:
            # التأكد من ترتيب المعرفات
            if start_id > end_id:
                start_id, end_id = end_id, start_id
            
            print(f"🔍 جلب الرسائل من {start_id} إلى {end_id} من قناة {channel}")
            
            # جلب الرسائل
            async for message in self.client.get_chat_history(
                channel, 
                limit=end_id - start_id + 100  # هامش أمان
            ):
                if message.id >= start_id and message.id <= end_id:
                    if message.text or message.caption:  # فقط الرسائل التي تحتوي على نص
                        messages.append(message)
                elif message.id < start_id:
                    break  # وصلنا لرسائل أقدم من المطلوب
            
            # ترتيب الرسائل حسب المعرف
            messages.sort(key=lambda x: x.id)
            
            print(f"✅ تم جلب {len(messages)} رسالة")
            return messages
            
        except ChannelPrivate:
            print(f"❌ القناة {channel} خاصة أو غير متاحة")
            return []
        except UsernameNotOccupied:
            print(f"❌ القناة {channel} غير موجودة")
            return []
        except FloodWait as e:
            print(f"⏳ انتظار {e.value} ثانية بسبب حد المعدل...")
            await asyncio.sleep(e.value)
            return await self.get_messages_range(channel, start_id, end_id)
        except Exception as e:
            print(f"❌ خطأ في جلب الرسائل من {channel}: {e}")
            return []
    
    async def get_message_content(self, url: str) -> Optional[str]:
        """استخراج محتوى رسالة من رابط"""
        channel_info = self.extract_channel_info(url)
        if not channel_info:
            print(f"❌ رابط غير صحيح: {url}")
            return None
        
        message = await self.get_message(
            channel_info['channel'], 
            channel_info['message_id']
        )
        
        if message:
            content = message.text or message.caption or ""
            return content.strip()
        
        return None
    
    async def analyze_posts_between_urls(self, first_url: str, last_url: str) -> Dict:
        """تحليل المنشورات بين رابطين"""
        # استخراج معلومات الروابط
        first_info = self.extract_channel_info(first_url)
        last_info = self.extract_channel_info(last_url)
        
        if not first_info or not last_info:
            return {
                'error': 'روابط غير صحيحة',
                'success': False
            }
        
        if first_info['channel'] != last_info['channel']:
            return {
                'error': 'الروابط يجب أن تكون من نفس القناة',
                'success': False
            }
        
        channel = first_info['channel']
        start_id = min(first_info['message_id'], last_info['message_id'])
        end_id = max(first_info['message_id'], last_info['message_id'])
        
        # جلب الرسائل
        messages = await self.get_messages_range(channel, start_id, end_id)
        
        if not messages:
            return {
                'error': 'لم يتم العثور على رسائل',
                'success': False
            }
        
        # تحليل الرسائل
        analysis = {
            'success': True,
            'channel': channel,
            'total_messages': len(messages),
            'start_date': messages[0].date if messages else None,
            'end_date': messages[-1].date if messages else None,
            'messages': []
        }
        
        for msg in messages:
            content = msg.text or msg.caption or ""
            analysis['messages'].append({
                'id': msg.id,
                'date': msg.date,
                'content': content[:200] + "..." if len(content) > 200 else content,
                'url': f"https://t.me/{channel}/{msg.id}"
            })
        
        return analysis

# دوال مساعدة للاستخدام المباشر
async def get_post_content_from_url(url: str) -> Optional[str]:
    """جلب محتوى منشور من رابط تيليجرام"""
    reader = TelegramReader()
    
    if not await reader.initialize():
        return None
    
    try:
        content = await reader.get_message_content(url)
        return content
    finally:
        await reader.close()

async def analyze_telegram_posts(first_url: str, last_url: str) -> Dict:
    """تحليل المنشورات بين رابطين في تيليجرام"""
    reader = TelegramReader()
    
    if not await reader.initialize():
        return {
            'error': 'فشل في تهيئة قارئ تيليجرام',
            'success': False
        }
    
    try:
        analysis = await reader.analyze_posts_between_urls(first_url, last_url)
        return analysis
    finally:
        await reader.close()

# اختبار سريع
async def test_telegram_reader():
    """اختبار قارئ تيليجرام"""
    print("🧪 اختبار قارئ تيليجرام...")
    
    reader = TelegramReader()
    
    if await reader.initialize():
        print("✅ تم تهيئة القارئ بنجاح")
        
        # يمكنك إضافة اختبار هنا برابط حقيقي
        # test_url = "https://t.me/channel/123"
        # content = await reader.get_message_content(test_url)
        # print(f"المحتوى: {content}")
        
        await reader.close()
        return True
    else:
        print("❌ فشل في تهيئة القارئ")
        return False

if __name__ == "__main__":
    asyncio.run(test_telegram_reader())
