#!/usr/bin/env python3
"""
سكريبت إعداد البوت
"""

import os
import sys

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    try:
        import subprocess
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ تم تثبيت المتطلبات بنجاح")
            return True
        else:
            print(f"❌ فشل في تثبيت المتطلبات: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def setup_config():
    """إعداد ملف التكوين"""
    print("\n⚙️ إعداد البوت...")
    
    # طلب رمز البوت
    bot_token = input("أدخل رمز البوت (Bot Token): ").strip()
    
    if not bot_token:
        print("❌ يجب إدخال رمز البوت")
        return False
    
    # طلب المنشورات المحفوظة
    print("\nأدخل المنشورات المحفوظة (اضغط Enter مرتين للانتهاء):")
    saved_posts = []
    while True:
        post = input(f"منشور {len(saved_posts) + 1}: ").strip()
        if not post:
            break
        saved_posts.append(post)
    
    if not saved_posts:
        print("⚠️ لم يتم إدخال منشورات محفوظة، سيتم استخدام منشورات تجريبية")
        saved_posts = [
            "منشور تجريبي أول",
            "منشور تجريبي ثاني",
            "منشور تجريبي ثالث"
        ]
    
    # طلب نسبة التشابه
    try:
        similarity = float(input("أدخل نسبة التشابه المطلوبة (0.8 افتراضي): ") or "0.8")
        if not 0 <= similarity <= 1:
            similarity = 0.8
    except ValueError:
        similarity = 0.8
    
    # كتابة ملف التكوين
    config_content = f'''# إعدادات البوت
BOT_TOKEN = "{bot_token}"

# المنشورات المحفوظة للمقارنة
SAVED_POSTS = [
{chr(10).join(f'    "{post}",' for post in saved_posts)}
]

# نسبة التشابه المطلوبة
SIMILARITY_THRESHOLD = {similarity}
'''
    
    try:
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("✅ تم إنشاء ملف config.py بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء ملف التكوين: {e}")
        return False

def run_tests():
    """تشغيل الاختبارات"""
    print("\n🧪 تشغيل الاختبارات...")
    try:
        import test_bot
        if test_bot.main():
            print("✅ جميع الاختبارات نجحت")
            return True
        else:
            print("❌ فشلت بعض الاختبارات")
            return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات: {e}")
        return False

def main():
    """الدالة الرئيسية للإعداد"""
    print("🤖 مرحباً بك في إعداد بوت تحليل المنشورات")
    print("=" * 50)
    
    # فحص إصدار Python
    if not check_python_version():
        return
    
    # تثبيت المتطلبات
    if not install_requirements():
        return
    
    # إعداد التكوين
    if not setup_config():
        return
    
    # تشغيل الاختبارات
    if not run_tests():
        print("⚠️ تحذير: فشلت بعض الاختبارات، لكن يمكنك المتابعة")
    
    print("\n🎉 تم إعداد البوت بنجاح!")
    print("\n📋 الخطوات التالية:")
    print("1. تشغيل البوت: python bot.py")
    print("2. ابدأ محادثة مع البوت على تيليجرام")
    print("3. أرسل /start للبدء")
    
    # سؤال عن تشغيل البوت
    run_now = input("\nهل تريد تشغيل البوت الآن؟ (y/n): ").lower().strip()
    if run_now in ['y', 'yes', 'نعم']:
        print("\n🚀 تشغيل البوت...")
        try:
            import bot
            bot.main()
        except KeyboardInterrupt:
            print("\n👋 تم إيقاف البوت")
        except Exception as e:
            print(f"\n❌ خطأ في تشغيل البوت: {e}")

if __name__ == "__main__":
    main()
