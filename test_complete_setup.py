#!/usr/bin/env python3
"""
اختبار شامل للإعداد الكامل
"""

import asyncio

def test_config():
    """اختبار ملف الإعدادات"""
    print("🔍 اختبار ملف الإعدادات...")
    
    try:
        from config import BOT_TOKEN, API_ID, API_HASH, SAVED_POSTS, SIMILARITY_THRESHOLD
        
        # فحص BOT_TOKEN
        if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
            print("❌ BOT_TOKEN غير معد")
            return False
        else:
            print(f"✅ BOT_TOKEN معد: {BOT_TOKEN[:10]}...")
        
        # فحص API_ID
        if API_ID == "YOUR_API_ID_HERE":
            print("❌ API_ID غير معد")
            return False
        else:
            print(f"✅ API_ID معد: {API_ID}")
        
        # فحص API_HASH
        if API_HASH == "YOUR_API_HASH_HERE":
            print("❌ API_HASH غير معد")
            return False
        else:
            print(f"✅ API_HASH معد: {API_HASH[:10]}...")
        
        print(f"✅ عدد المنشورات المحفوظة: {len(SAVED_POSTS)}")
        print(f"✅ نسبة التشابه: {SIMILARITY_THRESHOLD}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في config.py: {e}")
        return False

def test_imports():
    """اختبار استيراد المكتبات"""
    print("\n📚 اختبار المكتبات...")
    
    libraries = [
        ('telegram', 'python-telegram-bot'),
        ('pyrogram', 'pyrogram'),
        ('tgcrypto', 'tgcrypto (اختياري)'),
    ]
    
    success = True
    
    for lib_name, display_name in libraries:
        try:
            __import__(lib_name)
            print(f"✅ {display_name}")
        except ImportError:
            if lib_name == 'tgcrypto':
                print(f"⚠️ {display_name} - غير مثبت (سيعمل البوت لكن بشكل أبطأ)")
            else:
                print(f"❌ {display_name} - غير مثبت")
                success = False
    
    return success

async def test_telegram_connection():
    """اختبار الاتصال بتيليجرام"""
    print("\n🔗 اختبار الاتصال بتيليجرام...")
    
    try:
        from telegram import Bot
        from config import BOT_TOKEN
        
        bot = Bot(token=BOT_TOKEN)
        me = await bot.get_me()
        print(f"✅ البوت متصل: {me.first_name} (@{me.username})")
        return True
        
    except Exception as e:
        print(f"❌ فشل الاتصال بتيليجرام: {e}")
        return False

async def test_pyrogram_connection():
    """اختبار اتصال Pyrogram"""
    print("\n🔧 اختبار Pyrogram...")
    
    try:
        from pyrogram import Client
        from config import API_ID, API_HASH, BOT_TOKEN
        
        client = Client(
            "test_session_complete",
            api_id=API_ID,
            api_hash=API_HASH,
            bot_token=BOT_TOKEN
        )
        
        await client.start()
        me = await client.get_me()
        print(f"✅ Pyrogram متصل: {me.first_name}")
        await client.stop()
        
        # حذف ملف الجلسة
        import os
        session_file = "test_session_complete.session"
        if os.path.exists(session_file):
            os.remove(session_file)
        
        return True
        
    except Exception as e:
        print(f"❌ فشل اتصال Pyrogram: {e}")
        return False

def test_url_patterns():
    """اختبار أنماط الروابط"""
    print("\n🔗 اختبار أنماط الروابط...")
    
    try:
        from telegram_reader import TelegramReader
        
        reader = TelegramReader()
        
        test_urls = [
            "https://t.me/telegram/123",
            "https://t.me/durov/456",
            "https://telegram.me/channel/789"
        ]
        
        for url in test_urls:
            info = reader.extract_channel_info(url)
            if info:
                print(f"✅ {url} -> {info['channel']}/{info['message_id']}")
            else:
                print(f"❌ فشل في تحليل: {url}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الروابط: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل للإعداد الكامل")
    print("=" * 40)
    
    success = True
    
    # اختبار الإعدادات
    if not test_config():
        success = False
    
    # اختبار المكتبات
    if not test_imports():
        success = False
    
    # اختبار الاتصال بتيليجرام
    if not await test_telegram_connection():
        success = False
    
    # اختبار Pyrogram
    if not await test_pyrogram_connection():
        success = False
    
    # اختبار أنماط الروابط
    if not test_url_patterns():
        success = False
    
    print("\n" + "=" * 40)
    
    if success:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n📋 الخطوات التالية:")
        print("1. شغل البوت: python complete_bot.py")
        print("2. أو استخدم: run_complete_bot.bat")
        print("3. ابحث عن البوت في تيليجرام")
        print("4. أرسل /start")
        print("5. جرب رابط قناة تيليجرام")
        
        print("\n💡 مثال على الاستخدام:")
        print("- أرسل /start")
        print("- اختر 'تقرير عرض النشر اليومي'")
        print("- أرسل: https://t.me/telegram/1")
        print("- أرسل: https://t.me/telegram/10")
        print("- اضغط 'ابدأ الحساب'")
        
    else:
        print("❌ بعض الاختبارات فشلت")
        print("\n💡 نصائح:")
        print("- تأكد من تثبيت جميع المكتبات")
        print("- تحقق من API_ID و API_HASH")
        print("- تأكد من اتصال الإنترنت")
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if not result:
            print("\n🔧 للمساعدة:")
            print("- شغل: python test_api.py")
            print("- راجع: HOW_TO_GET_API_CREDENTIALS.md")
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
    
    input("\nاضغط Enter للخروج...")
