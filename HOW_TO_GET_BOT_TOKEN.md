# كيفية الحصول على رمز البوت (Bot Token)

## الخطوات:

### 1. فتح تيليجرام
- افتح تطبيق تيليجرام على هاتفك أو الكمبيوتر

### 2. البحث عن BotFather
- ابحث عن `@BotFather` في تيليجرام
- أو اذهب إلى الرابط: https://t.me/botfather

### 3. بدء محادثة مع BotFather
- اضغط على "Start" أو أرسل `/start`

### 4. إنشاء بوت جديد
- أرسل الأمر: `/newbot`
- سيطلب منك BotFather اسم البوت (يمكن أن يكون أي اسم)
- مثال: `بوت تحليل المنشورات`

### 5. اختيار اسم المستخدم للبوت
- سيطلب منك اسم مستخدم للبوت (يجب أن ينتهي بـ `bot`)
- مثال: `my_posts_analysis_bot`
- يجب أن يكون فريداً (غير مستخدم من قبل)

### 6. الحصول على الرمز
- بعد إنشاء البوت بنجاح، سيرسل لك BotFather رسالة تحتوي على:
  - رمز البوت (Bot Token)
  - رابط البوت

### 7. نسخ الرمز
- انسخ الرمز الذي يبدو مثل:
  ```
  1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
  ```

### 8. وضع الرمز في البوت
- افتح ملف `config.py`
- استبدل `YOUR_BOT_TOKEN_HERE` بالرمز الذي حصلت عليه:
  ```python
  BOT_TOKEN = "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz"
  ```

## ملاحظات مهمة:

⚠️ **احتفظ بالرمز سرياً**: لا تشارك رمز البوت مع أحد، فهو يعطي تحكماً كاملاً في البوت

🔒 **أمان الرمز**: إذا تم تسريب الرمز، يمكنك إنشاء رمز جديد عبر BotFather باستخدام `/revoke`

📝 **تخصيص البوت**: يمكنك تخصيص البوت لاحقاً عبر BotFather:
- تغيير الوصف: `/setdescription`
- تغيير الصورة: `/setuserpic`
- إضافة أوامر: `/setcommands`

## مثال كامل:

```
المستخدم: /newbot
BotFather: Alright, a new bot. How are we going to call it? Please choose a name for your bot.

المستخدم: بوت تحليل المنشورات
BotFather: Good. Now let's choose a username for your bot. It must end in `bot`. Like this, for example: TetrisBot or tetris_bot.

المستخدم: posts_analysis_bot
BotFather: Done! Congratulations on your new bot. You will find it at t.me/posts_analysis_bot. You can now add a description, about section and profile picture for your bot, see /help for a list of commands. By the way, when you've finished creating your cool bot, ping our Bot Support if you want a better username for it. Just make sure the bot is fully operational before you do this.

Use this token to access the HTTP API:
1234567890:ABCdefGHIjklMNOpqrsTUVwxyz

Keep your token secure and store it safely, it can be used by anyone to control your bot.
```

الرمز في هذا المثال هو: `1234567890:ABCdefGHIjklMNOpqrsTUVwxyz`
