#!/usr/bin/env python3
"""
فحص سريع للبوت
"""

import asyncio

# رمز البوت
BOT_TOKEN = "8415486238:AAFpoUZq_OXzZh74o8GrHmXXI7LwqPikSxI"

async def test_bot():
    """اختبار البوت"""
    print("🔍 فحص البوت...")
    
    try:
        # استيراد المكتبات
        from telegram import Bot
        print("✅ تم استيراد مكتبة telegram")
        
        # إنشاء البوت
        bot = Bot(token=BOT_TOKEN)
        print("✅ تم إنشاء البوت")
        
        # اختبار الاتصال
        me = await bot.get_me()
        print(f"✅ الاتصال ناجح - اسم البوت: {me.first_name}")
        print(f"✅ معرف البوت: @{me.username}")
        
        # اختبار إرسال رسالة (للمطور فقط)
        # يمكنك إضافة معرف المحادثة الخاص بك هنا للاختبار
        # chat_id = "YOUR_CHAT_ID"  # ضع معرف المحادثة الخاص بك
        # await bot.send_message(chat_id=chat_id, text="🧪 اختبار البوت - يعمل بنجاح!")
        
        print("\n🎉 البوت يعمل بشكل صحيح!")
        print(f"🔗 رابط البوت: https://t.me/{me.username}")
        
        return True
        
    except ImportError as e:
        print(f"❌ مكتبة telegram غير متاحة: {e}")
        print("💡 قم بتثبيتها: python -m pip install python-telegram-bot")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        print("💡 تحقق من:")
        print("   - اتصال الإنترنت")
        print("   - صحة رمز البوت")
        return False

def main():
    """الدالة الرئيسية"""
    print("🤖 فحص بوت تحليل المنشورات")
    print("=" * 40)
    
    try:
        result = asyncio.run(test_bot())
        
        if result:
            print("\n📋 الخطوات التالية:")
            print("1. شغل البوت: python working_bot.py")
            print("2. ابحث عن البوت في تيليجرام")
            print("3. أرسل /start للبوت")
            print("4. يجب أن يرد البوت بالقائمة الرئيسية")
        else:
            print("\n❌ يجب حل المشاكل أولاً")
            
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
