import logging
from datetime import datetime, timedelta
from typing import Dict, List
import re

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes, ConversationHandler

from config import BOT_TOKEN, SAVED_POSTS, SIMILARITY_THRESHOLD, API_ID, API_HASH
from utils import extract_post_content, get_post_date_from_url, check_post_similarity, generate_date_range
from telegram_reader import analyze_telegram_posts, get_post_content_from_url

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# حالات المحادثة
WAITING_FIRST_URL, WAITING_LAST_URL = range(2)

# تخزين بيانات المستخدمين
user_data: Dict[int, Dict] = {}

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج أمر /start"""
    keyboard = [
        [InlineKeyboardButton("📊 تقرير عرض النشر اليومي", callback_data='daily_report')],
        [InlineKeyboardButton("📢 تعميم آخر نشر", callback_data='latest_post')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    welcome_message = """
🤖 مرحباً بك في بوت تحليل المنشورات!

اختر أحد الخيارات التالية:
    """
    
    await update.message.reply_text(welcome_message, reply_markup=reply_markup)

async def button_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """معالج الأزرار"""
    query = update.callback_query
    await query.answer()
    
    user_id = query.from_user.id
    
    if query.data == 'daily_report':
        # بدء عملية تقرير النشر اليومي
        user_data[user_id] = {'mode': 'daily_report'}
        
        await query.edit_message_text(
            "📊 تقرير عرض النشر اليومي\n\n"
            "الرجاء إرسال رابط أول منشور:"
        )
        return WAITING_FIRST_URL
        
    elif query.data == 'latest_post':
        # تعميم آخر نشر
        await query.edit_message_text(
            "📢 تعميم آخر نشر\n\n"
            "هذه الميزة قيد التطوير..."
        )
        return ConversationHandler.END
    
    return ConversationHandler.END

async def handle_first_url(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """معالج رابط أول منشور"""
    user_id = update.from_user.id
    url = update.message.text.strip()
    
    # التحقق من صحة الرابط
    if not re.match(r'https?://', url):
        await update.message.reply_text(
            "❌ الرجاء إرسال رابط صحيح يبدأ بـ http:// أو https://"
        )
        return WAITING_FIRST_URL
    
    # حفظ الرابط الأول
    if user_id not in user_data:
        user_data[user_id] = {}
    
    user_data[user_id]['first_url'] = url
    
    await update.message.reply_text(
        "✅ تم حفظ رابط أول منشور\n\n"
        "الآن الرجاء إرسال رابط آخر منشور:"
    )
    
    return WAITING_LAST_URL

async def handle_last_url(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """معالج رابط آخر منشور"""
    user_id = update.from_user.id
    url = update.message.text.strip()
    
    # التحقق من صحة الرابط
    if not re.match(r'https?://', url):
        await update.message.reply_text(
            "❌ الرجاء إرسال رابط صحيح يبدأ بـ http:// أو https://"
        )
        return WAITING_LAST_URL
    
    # حفظ الرابط الأخير
    user_data[user_id]['last_url'] = url
    
    # إنشاء زر ابدأ الحساب
    keyboard = [[InlineKeyboardButton("🚀 ابدأ الحساب", callback_data='start_calculation')]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await update.message.reply_text(
        "✅ تم حفظ رابط آخر منشور\n\n"
        "اضغط على الزر أدناه لبدء عملية التحليل:",
        reply_markup=reply_markup
    )
    
    return ConversationHandler.END

async def start_calculation(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """بدء عملية الحساب والتحليل"""
    query = update.callback_query
    await query.answer()
    
    user_id = query.from_user.id
    
    if user_id not in user_data or 'first_url' not in user_data[user_id] or 'last_url' not in user_data[user_id]:
        await query.edit_message_text("❌ خطأ: لم يتم العثور على الروابط المطلوبة")
        return
    
    first_url = user_data[user_id]['first_url']
    last_url = user_data[user_id]['last_url']
    
    await query.edit_message_text("🔄 جاري تحليل المنشورات... الرجاء الانتظار")
    
    try:
        # تحليل المنشورات
        report = await analyze_posts(first_url, last_url)
        
        # إرسال التقرير
        await query.edit_message_text(report)
        
    except Exception as e:
        logger.error(f"خطأ في التحليل: {e}")
        await query.edit_message_text(f"❌ حدث خطأ أثناء التحليل: {str(e)}")

async def analyze_posts(first_url: str, last_url: str) -> str:
    """تحليل المنشورات بين رابطين"""
    try:
        # التحقق من نوع الروابط
        is_telegram_url = any(domain in first_url.lower() for domain in ['t.me', 'telegram.me', 'telegram.org'])

        if is_telegram_url:
            # استخدام قارئ تيليجرام للقنوات
            analysis = await analyze_telegram_posts(first_url, last_url)

            if not analysis['success']:
                return f"❌ خطأ في تحليل قناة تيليجرام: {analysis.get('error', 'خطأ غير معروف')}"

            # تحليل الرسائل المجلبة
            messages = analysis['messages']
            matching_posts = 0
            daily_report = []

            # تجميع الرسائل حسب التاريخ
            daily_messages = {}
            for msg in messages:
                date_str = msg['date'].strftime('%Y-%m-%d')
                if date_str not in daily_messages:
                    daily_messages[date_str] = []
                daily_messages[date_str].append(msg)

            # فحص كل يوم
            for date_str in sorted(daily_messages.keys()):
                day_messages = daily_messages[date_str]
                matching_count = 0

                for msg in day_messages:
                    if check_post_similarity(msg['content'], SAVED_POSTS, SIMILARITY_THRESHOLD):
                        matching_count += 1

                if matching_count > 0:
                    matching_posts += matching_count
                    if matching_count == 1:
                        daily_report.append(f"{date_str}: تم نشر منشور مطابق")
                    else:
                        daily_report.append(f"{date_str}: تم نشر {matching_count} منشورات مطابقة")
                else:
                    daily_report.append(f"{date_str}: لم يُنشر محتوى مطابق")

            days_without_posts = len([line for line in daily_report if "لم يُنشر" in line])

            # إنشاء التقرير النهائي
            report = f"""
📊 تقرير عرض النشر اليومي

📈 إحصائيات عامة:
• القناة: @{analysis['channel']}
• إجمالي الرسائل: {analysis['total_messages']}
• المنشورات المطابقة: {matching_posts}
• الأيام بدون نشر مطابق: {days_without_posts}
• إجمالي الأيام: {len(daily_messages)}

📅 التقرير اليومي:
{chr(10).join(daily_report)}

✅ تم إنشاء التقرير بنجاح
            """

        else:
            # استخدام الطريقة التقليدية للمواقع العادية
            first_post_content = extract_post_content(first_url)
            last_post_content = extract_post_content(last_url)

            if not first_post_content or not last_post_content:
                return "❌ لم يتمكن من استخراج محتوى المنشورات. تأكد من صحة الروابط."

            # استخراج التواريخ
            first_date = get_post_date_from_url(first_url)
            last_date = get_post_date_from_url(last_url)

            # التأكد من ترتيب التواريخ
            if first_date > last_date:
                first_date, last_date = last_date, first_date
                first_url, last_url = last_url, first_url

            # إنشاء قائمة بجميع التواريخ في المدى
            all_dates = generate_date_range(first_date, last_date)

            # تحليل بسيط للمنشورين الأول والأخير
            matching_posts = 0
            daily_report = []

            for i, date in enumerate(all_dates):
                date_str = date.strftime('%Y-%m-%d')

                if i == 0:  # أول منشور
                    if check_post_similarity(first_post_content, SAVED_POSTS, SIMILARITY_THRESHOLD):
                        daily_report.append(f"{date_str}: تم نشر منشور مطابق (الأول)")
                        matching_posts += 1
                    else:
                        daily_report.append(f"{date_str}: منشور غير مطابق")
                elif i == len(all_dates) - 1:  # آخر منشور
                    if check_post_similarity(last_post_content, SAVED_POSTS, SIMILARITY_THRESHOLD):
                        daily_report.append(f"{date_str}: تم نشر منشور مطابق (الأخير)")
                        matching_posts += 1
                    else:
                        daily_report.append(f"{date_str}: منشور غير مطابق")
                else:
                    daily_report.append(f"{date_str}: غير محلل (يحتاج روابط تيليجرام للتحليل الكامل)")

            days_without_posts = len([line for line in daily_report if "غير مطابق" in line or "غير محلل" in line])

            # إنشاء التقرير النهائي
            report = f"""
📊 تقرير عرض النشر اليومي

📈 إحصائيات عامة:
• المنشورات المطابقة: {matching_posts}
• الأيام بدون نشر مطابق: {days_without_posts}
• إجمالي الأيام: {len(all_dates)}

📅 التقرير اليومي:
{chr(10).join(daily_report)}

💡 ملاحظة: للحصول على تحليل كامل، استخدم روابط قنوات تيليجرام

✅ تم إنشاء التقرير بنجاح
            """

        return report.strip()

    except Exception as e:
        logger.error(f"خطأ في تحليل المنشورات: {e}")
        return f"❌ حدث خطأ أثناء التحليل: {str(e)}"

async def cancel(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """إلغاء المحادثة"""
    await update.message.reply_text("تم إلغاء العملية.")
    return ConversationHandler.END

def main() -> None:
    """تشغيل البوت"""
    # إنشاء التطبيق
    application = Application.builder().token(BOT_TOKEN).build()
    
    # إعداد معالج المحادثة
    conv_handler = ConversationHandler(
        entry_points=[
            CommandHandler('start', start),
            CallbackQueryHandler(button_handler, pattern='^daily_report$')
        ],
        states={
            WAITING_FIRST_URL: [MessageHandler(filters.TEXT & ~filters.COMMAND, handle_first_url)],
            WAITING_LAST_URL: [MessageHandler(filters.TEXT & ~filters.COMMAND, handle_last_url)],
        },
        fallbacks=[CommandHandler('cancel', cancel)],
    )
    
    # إضافة المعالجات
    application.add_handler(conv_handler)
    application.add_handler(CallbackQueryHandler(button_handler, pattern='^latest_post$'))
    application.add_handler(CallbackQueryHandler(start_calculation, pattern='^start_calculation$'))
    
    # تشغيل البوت
    print("🤖 البوت يعمل الآن...")
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == '__main__':
    main()
