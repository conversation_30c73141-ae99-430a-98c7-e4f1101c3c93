#!/usr/bin/env python3
"""
بوت بسيط مضمون العمل
"""

import logging
import asyncio
import re
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes, ConversationHandler

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# رمز البوت
BOT_TOKEN = "8415486238:AAFpoUZq_OXzZh74o8GrHmXXI7LwqPikSxI"

# حالات المحادثة
WAITING_FIRST_URL, WAITING_LAST_URL = range(2)

# تخزين بيانات المستخدمين
user_data = {}

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج أمر /start"""
    try:
        keyboard = [
            [InlineKeyboardButton("📊 تقرير عرض النشر اليومي", callback_data='daily_report')],
            [InlineKeyboardButton("📢 تعميم آخر نشر", callback_data='latest_post')]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        welcome_message = """🤖 مرحباً بك في بوت تحليل المنشورات!

اختر أحد الخيارات التالية:"""
        
        await update.message.reply_text(welcome_message, reply_markup=reply_markup)
        logger.info(f"تم إرسال رسالة ترحيب للمستخدم {update.effective_user.id}")
        
    except Exception as e:
        logger.error(f"خطأ في معالج start: {e}")
        await update.message.reply_text("حدث خطأ. حاول مرة أخرى.")

async def button_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """معالج الأزرار"""
    try:
        query = update.callback_query
        await query.answer()

        user_id = query.from_user.id

        if query.data == 'daily_report':
            # بدء عملية تقرير النشر اليومي
            user_data[user_id] = {'mode': 'daily_report'}

            await query.edit_message_text(
                "📊 تقرير عرض النشر اليومي\n\n"
                "الرجاء إرسال رابط أول منشور:\n"
                "(مثال: https://example.com/post1)\n\n"
                "أو أرسل /cancel للإلغاء"
            )
            return WAITING_FIRST_URL

        elif query.data == 'latest_post':
            await query.edit_message_text(
                "📢 تعميم آخر نشر\n\n"
                "هذه الميزة قيد التطوير...\n\n"
                "أرسل /start للعودة للقائمة الرئيسية"
            )
            return ConversationHandler.END

        elif query.data == 'start_calculation':
            await start_calculation(update, context)
            return ConversationHandler.END

        logger.info(f"تم معالجة الزر {query.data} للمستخدم {query.from_user.id}")

    except Exception as e:
        logger.error(f"خطأ في معالج الأزرار: {e}")

    return ConversationHandler.END

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج أمر /help"""
    try:
        help_text = """🤖 بوت تحليل المنشورات

الأوامر المتاحة:
/start - بدء البوت وعرض القائمة الرئيسية
/help - عرض هذه المساعدة
/test - اختبار البوت

المميزات:
📊 تقرير عرض النشر اليومي
📢 تعميم آخر نشر (قيد التطوير)

للمساعدة: أرسل /start واتبع التعليمات"""
        
        await update.message.reply_text(help_text)
        logger.info(f"تم إرسال المساعدة للمستخدم {update.effective_user.id}")
        
    except Exception as e:
        logger.error(f"خطأ في معالج help: {e}")

async def test_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج أمر /test"""
    try:
        test_text = """✅ البوت يعمل بشكل طبيعي!

🔧 معلومات النظام:
• الحالة: متصل
• الوقت: متاح
• الاستجابة: سريعة

🎯 جرب الأوامر التالية:
/start - القائمة الرئيسية
/help - المساعدة"""
        
        await update.message.reply_text(test_text)
        logger.info(f"تم اختبار البوت للمستخدم {update.effective_user.id}")
        
    except Exception as e:
        logger.error(f"خطأ في معالج test: {e}")

async def error_handler(update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج الأخطاء"""
    logger.error(f"حدث خطأ: {context.error}")

def main() -> None:
    """تشغيل البوت"""
    print("🤖 بدء تشغيل البوت...")
    print(f"🔑 رمز البوت: {BOT_TOKEN[:10]}...")
    
    try:
        # إنشاء التطبيق
        application = Application.builder().token(BOT_TOKEN).build()
        
        # إضافة المعالجات
        application.add_handler(CommandHandler('start', start))
        application.add_handler(CommandHandler('help', help_command))
        application.add_handler(CommandHandler('test', test_command))
        application.add_handler(CallbackQueryHandler(button_handler))
        
        # إضافة معالج الأخطاء
        application.add_error_handler(error_handler)
        
        print("✅ تم إعداد جميع المعالجات")
        print("🚀 البوت يعمل الآن...")
        print("💡 ابحث عن البوت في تيليجرام وأرسل /start")
        print("💡 اضغط Ctrl+C لإيقاف البوت")
        print("-" * 50)
        
        # تشغيل البوت
        application.run_polling(
            allowed_updates=Update.ALL_TYPES,
            drop_pending_updates=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البوت: {e}")
        logger.error(f"خطأ في تشغيل البوت: {e}")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
