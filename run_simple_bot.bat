@echo off
chcp 65001 > nul
title بوت تحليل المنشورات - النسخة البسيطة
echo 🤖 بوت تحليل المنشورات - النسخة البسيطة
echo ===============================================
echo.

echo 🔍 فحص Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متاح
    pause
    exit /b 1
)

echo ✅ Python متاح
echo.

echo 🧪 فحص مكتبة telegram...
python -c "import telegram; print('✅ مكتبة telegram متاحة')" 2>nul
if errorlevel 1 (
    echo ❌ مكتبة telegram غير متاحة
    echo 💡 تثبيت المكتبة...
    python -m pip install python-telegram-bot
)

echo.
echo 🚀 تشغيل البوت البسيط...
echo.
echo 💡 هذا البوت بسيط ويعمل بدون مكتبات إضافية
echo 💡 يقرأ الروابط ويحسب العدد تلقائياً
echo 💡 ابحث عن البوت في تيليجرام وأرسل /start
echo 💡 اضغط Ctrl+C لإيقاف البوت
echo.

python simple_working_bot.py

echo.
echo 👋 تم إيقاف البوت
pause
