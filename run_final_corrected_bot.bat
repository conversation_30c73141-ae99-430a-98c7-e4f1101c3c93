@echo off
chcp 65001 > nul
title بوت تحليل المنشورات - النسخة النهائية المصححة
echo 🤖 بوت تحليل المنشورات - النسخة النهائية المصححة
echo ===============================================
echo.

echo 🔍 فحص Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متاح
    echo 💡 تأكد من تثبيت Python أولاً
    pause
    exit /b 1
)
echo ✅ Python متاح

echo.
echo 🧪 فحص مكتبة telegram...
python -c "import telegram; print('✅ مكتبة telegram متاحة')" 2>nul
if errorlevel 1 (
    echo ❌ مكتبة telegram غير متاحة
    echo 💡 تثبيت المكتبة...
    python -m pip install python-telegram-bot
    if errorlevel 1 (
        echo ❌ فشل في التثبيت
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبة
)

echo.
echo 🚀 تشغيل البوت النهائي المصحح...
echo.
echo ✅ النمط الجديد للتقرير:
echo   📅 التاريخ: منشور رقم 1
echo   📅 التاريخ: منشور رقم 2
echo   📅 التاريخ: لم يتم النشر
echo   📅 التاريخ: منشور رقم 3
echo   🔄 وهكذا بشكل متسلسل...
echo.
echo ✨ المميزات:
echo   🎯 يعرض جميع الأيام في النطاق الزمني
echo   📊 يحسب فقط المنشورات المطابقة للمحفوظة
echo   🔢 ترقيم صحيح: الرابط الأول = منشور رقم 1
echo   📅 تواريخ واقعية من آخر 30 يوم
echo   📋 عرض "لم يتم النشر" للأيام الفارغة
echo.
echo 💡 كيفية الاستخدام:
echo   1. ابحث عن البوت في تيليجرام
echo   2. اضغط /start من قائمة الأوامر
echo   3. اختر "📊 تقرير عرض النشر اليومي"
echo   4. أرسل رابط أول منشور
echo   5. أرسل رابط آخر منشور
echo   6. اضغط "🚀 ابدأ الحساب"
echo.
echo 🧪 للاختبار:
echo   - الأول: https://t.me/telegram/1
echo   - الأخير: https://t.me/telegram/10
echo   - النتيجة: تقرير يومي متسلسل
echo.
echo 📝 مثال على النتيجة:
echo   2024-12-15: منشور رقم 1
echo   2024-12-16: لم يتم النشر
echo   2024-12-17: منشور رقم 3
echo   2024-12-18: منشور رقم 5
echo   2024-12-19: لم يتم النشر
echo.
echo 💡 اضغط Ctrl+C لإيقاف البوت
echo.

python corrected_bot.py

echo.
echo 👋 تم إيقاف البوت
pause
