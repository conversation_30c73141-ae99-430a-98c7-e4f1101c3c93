@echo off
chcp 65001 > nul
echo 🔧 تثبيت متطلبات البوت...
echo.

REM التحقق من وجود Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت. الرجاء تثبيت Python من https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ Python متاح
echo.

echo 📦 تثبيت المكتبات المطلوبة...
echo.

REM تثبيت المكتبات واحدة تلو الأخرى
echo تثبيت python-telegram-bot...
python -m pip install python-telegram-bot==20.7
if errorlevel 1 (
    echo ❌ فشل في تثبيت python-telegram-bot
    pause
    exit /b 1
)

echo تثبيت requests...
python -m pip install requests==2.31.0
if errorlevel 1 (
    echo ❌ فشل في تثبيت requests
    pause
    exit /b 1
)

echo تثبيت beautifulsoup4...
python -m pip install beautifulsoup4==4.12.2
if errorlevel 1 (
    echo ❌ فشل في تثبيت beautifulsoup4
    pause
    exit /b 1
)

echo تثبيت lxml...
python -m pip install lxml==4.9.3
if errorlevel 1 (
    echo ❌ فشل في تثبيت lxml
    pause
    exit /b 1
)

echo تثبيت python-dateutil...
python -m pip install python-dateutil==2.8.2
if errorlevel 1 (
    echo ❌ فشل في تثبيت python-dateutil
    pause
    exit /b 1
)

echo تثبيت pyrogram...
python -m pip install pyrogram==2.0.106
if errorlevel 1 (
    echo ❌ فشل في تثبيت pyrogram
    pause
    exit /b 1
)

echo تثبيت tgcrypto...
python -m pip install tgcrypto==1.2.5
if errorlevel 1 (
    echo ❌ فشل في تثبيت tgcrypto
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت جميع المكتبات بنجاح!
echo.

echo 🧪 اختبار API credentials...
python test_api.py

echo.
echo 🎉 الإعداد مكتمل! يمكنك الآن تشغيل البوت باستخدام:
echo    python bot.py
echo أو
echo    run.bat
echo.
pause
