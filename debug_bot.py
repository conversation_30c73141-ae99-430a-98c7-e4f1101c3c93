#!/usr/bin/env python3
"""
نسخة مبسطة من البوت لتشخيص المشاكل
"""

print("🔍 بدء تشخيص البوت...")

# اختبار 1: استيراد المكتبات الأساسية
try:
    import sys
    import os
    print("✅ المكتبات الأساسية متاحة")
except Exception as e:
    print(f"❌ خطأ في المكتبات الأساسية: {e}")
    exit(1)

# اختبار 2: استيراد ملف الإعدادات
try:
    from config import BOT_TOKEN, SAVED_POSTS, SIMILARITY_THRESHOLD
    print("✅ ملف config.py تم استيراده بنجاح")
    
    if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
        print("❌ رمز البوت لم يتم إعداده")
        exit(1)
    else:
        print(f"✅ رمز البوت معد (يبدأ بـ: {BOT_TOKEN[:10]}...)")
        
except Exception as e:
    print(f"❌ خطأ في ملف config.py: {e}")
    exit(1)

# اختبار 3: استيراد مكتبة telegram
try:
    from telegram import Update
    from telegram.ext import Application
    print("✅ مكتبة python-telegram-bot متاحة")
except ImportError as e:
    print(f"❌ مكتبة python-telegram-bot غير متاحة: {e}")
    print("💡 قم بتثبيتها باستخدام: python -m pip install python-telegram-bot")
    exit(1)
except Exception as e:
    print(f"❌ خطأ في مكتبة telegram: {e}")
    exit(1)

# اختبار 4: إنشاء تطبيق البوت
try:
    application = Application.builder().token(BOT_TOKEN).build()
    print("✅ تم إنشاء تطبيق البوت بنجاح")
except Exception as e:
    print(f"❌ خطأ في إنشاء تطبيق البوت: {e}")
    print("💡 تحقق من صحة رمز البوت")
    exit(1)

# اختبار 5: اختبار الاتصال بـ Telegram
try:
    import asyncio
    
    async def test_connection():
        try:
            bot = application.bot
            me = await bot.get_me()
            print(f"✅ الاتصال بـ Telegram ناجح - اسم البوت: {me.first_name}")
            return True
        except Exception as e:
            print(f"❌ فشل الاتصال بـ Telegram: {e}")
            return False
    
    # تشغيل اختبار الاتصال
    result = asyncio.run(test_connection())
    
    if not result:
        print("💡 تحقق من:")
        print("   - اتصال الإنترنت")
        print("   - صحة رمز البوت")
        print("   - أن البوت لم يتم حذفه من BotFather")
        exit(1)
        
except Exception as e:
    print(f"❌ خطأ في اختبار الاتصال: {e}")
    exit(1)

print("\n🎉 جميع الاختبارات نجحت!")
print("البوت جاهز للعمل. جرب تشغيل: python bot.py")

# اختبار إضافي: محاولة تشغيل البوت لثوانٍ قليلة
try:
    print("\n🚀 اختبار تشغيل البوت لـ 10 ثوانٍ...")
    
    async def start_command(update, context):
        await update.message.reply_text("🤖 البوت يعمل بنجاح!")
    
    from telegram.ext import CommandHandler
    application.add_handler(CommandHandler('start', start_command))
    
    print("✅ تم إعداد معالج /start")
    print("💡 البوت سيعمل لـ 10 ثوانٍ للاختبار...")
    print("💡 أرسل /start للبوت في تيليجرام للاختبار")
    
    # تشغيل البوت لفترة قصيرة
    async def run_test():
        await application.initialize()
        await application.start()
        
        print("🟢 البوت يعمل الآن...")
        await asyncio.sleep(10)  # انتظار 10 ثوانٍ
        
        await application.stop()
        print("🔴 تم إيقاف البوت")
    
    asyncio.run(run_test())
    
except Exception as e:
    print(f"❌ خطأ في اختبار التشغيل: {e}")
    print("💡 لكن الإعدادات الأساسية تبدو صحيحة")

print("\n✅ انتهى التشخيص")
input("اضغط Enter للخروج...")
