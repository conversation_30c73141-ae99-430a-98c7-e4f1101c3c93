import difflib
import re
from datetime import datetime, timedelta
from typing import List, Dict, Tuple
import requests
from bs4 import BeautifulSoup

def calculate_similarity(text1: str, text2: str) -> float:
    """حساب نسبة التشابه بين نصين"""
    # تنظيف النصوص
    text1 = clean_text(text1)
    text2 = clean_text(text2)
    
    # حساب التشابه باستخدام SequenceMatcher
    similarity = difflib.SequenceMatcher(None, text1, text2).ratio()
    return similarity

def clean_text(text: str) -> str:
    """تنظيف النص من الرموز والمسافات الزائدة"""
    # إزالة الروابط
    text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
    # إزالة الرموز الخاصة
    text = re.sub(r'[^\w\s\u0600-\u06FF]', ' ', text)
    # إزالة المسافات الزائدة
    text = ' '.join(text.split())
    return text.strip()

def extract_post_content(url: str) -> str:
    """استخراج محتوى المنشور من الرابط"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # البحث عن محتوى المنشور (يمكن تعديل هذا حسب موقع المنشورات)
        # هذا مثال عام، قد تحتاج لتعديله حسب الموقع المستخدم
        content_selectors = [
            '.post-content',
            '.entry-content', 
            '.content',
            'article',
            '.post-body',
            'main'
        ]
        
        content = ""
        for selector in content_selectors:
            element = soup.select_one(selector)
            if element:
                content = element.get_text(strip=True)
                break
        
        if not content:
            # إذا لم نجد محتوى، نأخذ النص من body
            content = soup.get_text(strip=True)
        
        return content[:1000]  # نأخذ أول 1000 حرف فقط
        
    except Exception as e:
        print(f"خطأ في استخراج المحتوى من {url}: {e}")
        return ""

def get_post_date_from_url(url: str) -> datetime:
    """استخراج تاريخ المنشور من الرابط أو المحتوى"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # البحث عن التاريخ في meta tags
        date_selectors = [
            'meta[property="article:published_time"]',
            'meta[name="date"]',
            'meta[name="publish_date"]',
            '.post-date',
            '.entry-date',
            'time'
        ]
        
        for selector in date_selectors:
            element = soup.select_one(selector)
            if element:
                date_str = element.get('content') or element.get('datetime') or element.get_text()
                if date_str:
                    try:
                        # محاولة تحليل التاريخ
                        from dateutil import parser
                        return parser.parse(date_str)
                    except:
                        continue
        
        # إذا لم نجد التاريخ، نستخدم التاريخ الحالي
        return datetime.now()
        
    except Exception as e:
        print(f"خطأ في استخراج التاريخ من {url}: {e}")
        return datetime.now()

def check_post_similarity(post_content: str, saved_posts: List[str], threshold: float = 0.8) -> bool:
    """فحص ما إذا كان المنشور يتشابه مع المنشورات المحفوظة"""
    for saved_post in saved_posts:
        similarity = calculate_similarity(post_content, saved_post)
        if similarity >= threshold:
            return True
    return False

def generate_date_range(start_date: datetime, end_date: datetime) -> List[datetime]:
    """إنشاء قائمة بجميع التواريخ بين تاريخين"""
    dates = []
    current_date = start_date.date()
    end_date = end_date.date()
    
    while current_date <= end_date:
        dates.append(datetime.combine(current_date, datetime.min.time()))
        current_date += timedelta(days=1)
    
    return dates
