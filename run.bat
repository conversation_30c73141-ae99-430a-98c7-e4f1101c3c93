@echo off
chcp 65001 > nul
echo 🤖 تشغيل بوت تحليل المنشورات...
echo.

REM التحقق من وجود Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت. الرجاء تثبيت Python أولاً.
    pause
    exit /b 1
)

REM التحقق من وجود ملف config.py
if not exist config.py (
    echo ⚠️ ملف config.py غير موجود. سيتم تشغيل الإعداد...
    python setup.py
    if errorlevel 1 (
        echo ❌ فشل في الإعداد
        pause
        exit /b 1
    )
)

REM تشغيل البوت
echo 🚀 تشغيل البوت...
python bot.py

echo.
echo 👋 تم إيقاف البوت
pause
