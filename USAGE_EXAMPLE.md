# مثال على الاستخدام

## سيناريو الاستخدام

لنفترض أنك تريد تحليل المنشورات في موقع أخبار من تاريخ 1 يناير 2025 إلى 15 يناير 2025.

## الخطوات:

### 1. بدء المحادثة
```
المستخدم: /start
البوت: 🤖 مرحباً بك في بوت تحليل المنشورات!

اختر أحد الخيارات التالية:
[📊 تقرير عرض النشر اليومي] [📢 تعميم آخر نشر]
```

### 2. اختيار تقرير النشر اليومي
```
المستخدم: [يضغط على 📊 تقرير عرض النشر اليومي]
البوت: 📊 تقرير عرض النشر اليومي

الرجاء إرسال رابط أول منشور:
```

### 3. إرسال رابط أول منشور
```
المستخدم: https://example.com/post/2025-01-01-first-post
البوت: ✅ تم حفظ رابط أول منشور

الآن الرجاء إرسال رابط آخر منشور:
```

### 4. إرسال رابط آخر منشور
```
المستخدم: https://example.com/post/2025-01-15-last-post
البوت: ✅ تم حفظ رابط آخر منشور

اضغط على الزر أدناه لبدء عملية التحليل:
[🚀 ابدأ الحساب]
```

### 5. بدء التحليل
```
المستخدم: [يضغط على 🚀 ابدأ الحساب]
البوت: 🔄 جاري تحليل المنشورات... الرجاء الانتظار
```

### 6. النتيجة
```
البوت: 📊 تقرير عرض النشر اليومي

📈 إحصائيات عامة:
• عدد المنشورات المطابقة: 12
• عدد الأيام بدون نشر: 3
• إجمالي الأيام: 15

📅 التقرير اليومي:
2025-01-01: تم نشر منشور رقم 1
2025-01-02: تم نشر منشور رقم 2
2025-01-03: لم يُنشر
2025-01-04: تم نشر منشور رقم 3
2025-01-05: تم نشر منشور رقم 4
2025-01-06: تم نشر منشورين
2025-01-07: لم يُنشر
2025-01-08: تم نشر منشور رقم 5
2025-01-09: تم نشر منشور رقم 6
2025-01-10: تم نشر منشور رقم 7
2025-01-11: تم نشر منشور رقم 8
2025-01-12: لم يُنشر
2025-01-13: تم نشر منشور رقم 9
2025-01-14: تم نشر منشور رقم 10
2025-01-15: تم نشر منشور رقم 11

✅ تم إنشاء التقرير بنجاح
```

## شرح النتائج:

### الإحصائيات العامة:
- **عدد المنشورات المطابقة**: عدد المنشورات التي تشابهت مع المنشورات المحفوظة بنسبة ≥ 80%
- **عدد الأيام بدون نشر**: الأيام التي لم يتم نشر أي منشور مطابق فيها
- **إجمالي الأيام**: العدد الكلي للأيام في المدة المحددة

### التقرير اليومي:
- **تم نشر منشور رقم X**: تم نشر منشور واحد مطابق في هذا اليوم
- **تم نشر منشورين**: تم نشر أكثر من منشور مطابق في نفس اليوم
- **لم يُنشر**: لم يتم نشر أي منشور مطابق في هذا اليوم

## ملاحظات:

1. **ترقيم المنشورات**: يبدأ من 1 للمنشور الأول ويزيد تدريجياً
2. **التواريخ**: بصيغة YYYY-MM-DD (سنة-شهر-يوم)
3. **المطابقة**: تعتمد على نسبة التشابه المحددة في الإعدادات (افتراضياً 80%)
4. **الأيام المتعددة**: إذا تم نشر أكثر من منشور في نفس اليوم، يُحسب اليوم مرة واحدة

## أمثلة على الروابط المدعومة:

```
# مواقع الأخبار
https://example-news.com/article/123
https://news-site.com/2025/01/15/article-title

# المدونات
https://myblog.com/post/article-title
https://blog.example.com/2025/01/post-name

# منصات النشر
https://medium.com/@user/article-title
https://dev.to/user/article-title
```

## نصائح للاستخدام الأمثل:

1. **تأكد من صحة الروابط**: يجب أن تكون الروابط قابلة للوصول
2. **ترتيب التواريخ**: الرابط الأول يجب أن يكون للمنشور الأقدم
3. **المنشورات المحفوظة**: أضف منشورات متنوعة في config.py للحصول على نتائج أفضل
4. **نسبة التشابه**: يمكن تعديلها حسب الحاجة (0.6 للتشابه المرن، 0.9 للتشابه الصارم)
