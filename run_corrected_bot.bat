@echo off
chcp 65001 > nul
title بوت تحليل المنشورات - النسخة المصححة
echo 🤖 بوت تحليل المنشورات - النسخة المصححة
echo ===============================================
echo.

echo 🔍 فحص Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متاح
    echo 💡 تأكد من تثبيت Python أولاً
    pause
    exit /b 1
)
echo ✅ Python متاح

echo.
echo 🧪 فحص مكتبة telegram...
python -c "import telegram; print('✅ مكتبة telegram متاحة')" 2>nul
if errorlevel 1 (
    echo ❌ مكتبة telegram غير متاحة
    echo 💡 تثبيت المكتبة...
    python -m pip install python-telegram-bot
    if errorlevel 1 (
        echo ❌ فشل في التثبيت
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبة
)

echo.
echo 🚀 تشغيل البوت المصحح...
echo.
echo ✅ الإصلاحات المطبقة:
echo   📅 تواريخ واقعية (آخر 30 يوم)
echo   🎯 يحسب فقط المنشورات المطابقة للمحفوظة
echo   🔢 ترقيم صحيح: الرابط الأول = منشور رقم 1
echo   📊 إحصائيات دقيقة للمطابقة فقط
echo   📋 عرض "المنشور رقم X" مع التاريخ الصحيح
echo.
echo 💡 كيفية الاستخدام:
echo   1. ابحث عن البوت في تيليجرام
echo   2. اضغط /start من قائمة الأوامر
echo   3. اختر "📊 تقرير عرض النشر اليومي"
echo   4. أرسل رابط أول منشور
echo   5. أرسل رابط آخر منشور
echo   6. اضغط "🚀 ابدأ الحساب"
echo.
echo 🧪 للاختبار:
echo   - الأول: https://t.me/telegram/1
echo   - الأخير: https://t.me/telegram/10
echo   - النتيجة: فقط المنشورات المطابقة مع تواريخ صحيحة
echo.
echo 📝 ملاحظات مهمة:
echo   - البوت يعرض فقط المنشورات المطابقة للمحفوظة
echo   - التواريخ واقعية من آخر 30 يوم
echo   - الترقيم يبدأ من 1 للرابط الأول
echo   - لا يعرض الأيام الفارغة أو المنشورات غير المطابقة
echo.
echo 💡 اضغط Ctrl+C لإيقاف البوت
echo.

python corrected_bot.py

echo.
echo 👋 تم إيقاف البوت
pause
