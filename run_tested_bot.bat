@echo off
chcp 65001 > nul
title بوت تحليل المنشورات - مختبر ومضمون
echo 🤖 بوت تحليل المنشورات - مختبر ومضمون
echo ===============================================
echo.

echo 🔍 فحص Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متاح
    pause
    exit /b 1
)
echo ✅ Python متاح

echo.
echo 🧪 فحص مكتبة telegram...
python -c "import telegram; print('✅ مكتبة telegram متاحة')" 2>nul
if errorlevel 1 (
    echo ❌ مكتبة telegram غير متاحة
    echo 💡 تثبيت المكتبة...
    python -m pip install python-telegram-bot
    if errorlevel 1 (
        echo ❌ فشل في التثبيت
        pause
        exit /b 1
    )
)

echo.
echo 🚀 تشغيل البوت المختبر...
echo.
echo 💡 هذا البوت تم اختباره ويعمل بشكل مضمون
echo 💡 يقرأ الروابط ويحسب العدد تلقائياً
echo 💡 ابحث عن البوت في تيليجرام وأرسل /start
echo 💡 اضغط Ctrl+C لإيقاف البوت
echo.

python tested_bot.py

echo.
echo 👋 تم إيقاف البوت
pause
