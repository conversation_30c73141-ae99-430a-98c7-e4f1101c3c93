#!/usr/bin/env python3
"""
اختبار التواريخ المصححة
"""

from datetime import datetime, timedelta
import random

def generate_correct_date(post_number: int) -> datetime:
    """إنشاء تاريخ صحيح للمنشور - تواريخ حقيقية من الماضي"""
    # تاريخ البداية: قبل شهرين من الآن
    base_date = datetime.now() - timedelta(days=60)
    
    # كل منشور في يوم منفصل، بدءاً من التاريخ الأساسي
    days_offset = post_number - 1  # المنشور الأول في اليوم الأول
    hours = random.randint(10, 20)  # ساعات النشر الطبيعية
    minutes = random.randint(0, 59)
    
    # حساب التاريخ الصحيح
    post_date = base_date + timedelta(days=days_offset)
    post_date = post_date.replace(hour=hours, minute=minutes, second=0, microsecond=0)
    
    # التأكد من أن التاريخ في الماضي
    if post_date > datetime.now():
        post_date = datetime.now() - timedelta(days=post_number)
    
    return post_date

def test_dates():
    """اختبار التواريخ"""
    print("🧪 اختبار التواريخ المصححة")
    print("=" * 40)
    
    current_time = datetime.now()
    print(f"📅 التاريخ الحالي: {current_time.strftime('%Y-%m-%d %H:%M')}")
    print()
    
    print("📊 تواريخ المنشورات المطابقة:")
    for i in range(1, 11):
        post_date = generate_correct_date(i)
        is_past = post_date < current_time
        status = "✅ ماضي" if is_past else "❌ مستقبل"
        
        print(f"منشور رقم {i:2d}: {post_date.strftime('%Y-%m-%d %H:%M')} ({status})")
    
    print()
    print("🔍 فحص التواريخ:")
    
    # فحص أن جميع التواريخ في الماضي
    all_past = True
    for i in range(1, 11):
        post_date = generate_correct_date(i)
        if post_date >= current_time:
            all_past = False
            break
    
    if all_past:
        print("✅ جميع التواريخ في الماضي")
    else:
        print("❌ بعض التواريخ في المستقبل")
    
    # فحص التسلسل
    dates = [generate_correct_date(i) for i in range(1, 6)]
    is_sequential = all(dates[i] < dates[i+1] for i in range(len(dates)-1))
    
    if is_sequential:
        print("✅ التواريخ متسلسلة بشكل صحيح")
    else:
        print("❌ التواريخ غير متسلسلة")
    
    # فحص النطاق الزمني
    base_date = current_time - timedelta(days=60)
    max_date = current_time - timedelta(days=1)
    
    all_in_range = True
    for i in range(1, 11):
        post_date = generate_correct_date(i)
        if not (base_date <= post_date <= max_date):
            all_in_range = False
            break
    
    if all_in_range:
        print("✅ جميع التواريخ في النطاق المطلوب (آخر شهرين)")
    else:
        print("❌ بعض التواريخ خارج النطاق")
    
    print()
    print("📋 ملخص:")
    print(f"📅 نطاق التواريخ: من {base_date.strftime('%Y-%m-%d')} إلى {max_date.strftime('%Y-%m-%d')}")
    print("🎯 كل منشور في يوم منفصل")
    print("⏰ أوقات النشر: من 10 صباحاً إلى 8 مساءً")
    print("✅ جميع التواريخ في الماضي")

if __name__ == "__main__":
    test_dates()
    input("\nاضغط Enter للخروج...")
