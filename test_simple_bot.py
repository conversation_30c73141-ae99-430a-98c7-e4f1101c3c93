#!/usr/bin/env python3
"""
اختبار البوت البسيط
"""

import re

def test_url_extraction():
    """اختبار استخراج معلومات الروابط"""
    print("🔗 اختبار استخراج الروابط...")
    
    def extract_telegram_info(url: str):
        patterns = [
            r't\.me/([^/]+)/(\d+)',
            r'telegram\.me/([^/]+)/(\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return {
                    'channel': match.group(1),
                    'message_id': int(match.group(2))
                }
        return None
    
    test_urls = [
        "https://t.me/telegram/1",
        "https://t.me/durov/123",
        "https://telegram.me/channel/456",
        "https://t.me/news_channel/789"
    ]
    
    for url in test_urls:
        info = extract_telegram_info(url)
        if info:
            print(f"✅ {url} -> @{info['channel']}/{info['message_id']}")
        else:
            print(f"❌ فشل في تحليل: {url}")
    
    return True

def test_count_calculation():
    """اختبار حساب عدد المنشورات"""
    print("\n🔢 اختبار حساب العدد...")
    
    test_cases = [
        (1, 10, 10),    # من 1 إلى 10 = 10 منشورات
        (5, 15, 11),   # من 5 إلى 15 = 11 منشور
        (100, 200, 101), # من 100 إلى 200 = 101 منشور
        (10, 5, 6),    # من 5 إلى 10 = 6 منشورات (ترتيب عكسي)
    ]
    
    for first_id, last_id, expected in test_cases:
        start_id = min(first_id, last_id)
        end_id = max(first_id, last_id)
        total = end_id - start_id + 1
        
        if total == expected:
            print(f"✅ من {first_id} إلى {last_id} = {total} منشور")
        else:
            print(f"❌ من {first_id} إلى {last_id} = {total} منشور (متوقع: {expected})")
    
    return True

def test_telegram_import():
    """اختبار استيراد مكتبة telegram"""
    print("\n📚 اختبار مكتبة telegram...")
    
    try:
        from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, BotCommand
        from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes
        print("✅ مكتبة python-telegram-bot متاحة")
        return True
    except ImportError as e:
        print(f"❌ مكتبة python-telegram-bot غير متاحة: {e}")
        print("💡 قم بتثبيتها: python -m pip install python-telegram-bot")
        return False

def test_bot_token():
    """اختبار رمز البوت"""
    print("\n🔑 اختبار رمز البوت...")
    
    BOT_TOKEN = "8415486238:AAFpoUZq_OXzZh74o8GrHmXXI7LwqPikSxI"
    
    if BOT_TOKEN and len(BOT_TOKEN) > 20:
        print(f"✅ رمز البوت: {BOT_TOKEN[:10]}...")
        return True
    else:
        print("❌ رمز البوت غير صحيح")
        return False

def simulate_user_flow():
    """محاكاة تدفق المستخدم"""
    print("\n👤 محاكاة تدفق المستخدم...")
    
    # محاكاة بيانات المستخدم
    user_data = {}
    user_id = 12345
    
    # الخطوة 1: بدء المحادثة
    user_data[user_id] = {"state": "idle"}
    print("✅ بدء المحادثة")
    
    # الخطوة 2: اختيار تقرير النشر
    user_data[user_id]["state"] = "waiting_first_url"
    print("✅ اختيار تقرير النشر اليومي")
    
    # الخطوة 3: إدخال الرابط الأول
    first_url = "https://t.me/telegram/1"
    def extract_telegram_info(url):
        match = re.search(r't\.me/([^/]+)/(\d+)', url)
        if match:
            return {'channel': match.group(1), 'message_id': int(match.group(2))}
        return None
    
    info = extract_telegram_info(first_url)
    if info:
        user_data[user_id].update({
            "state": "waiting_last_url",
            "first_url": first_url,
            "first_channel": info['channel'],
            "first_message_id": info['message_id']
        })
        print(f"✅ حفظ الرابط الأول: @{info['channel']}/{info['message_id']}")
    
    # الخطوة 4: إدخال الرابط الأخير
    last_url = "https://t.me/telegram/10"
    info = extract_telegram_info(last_url)
    if info and info['channel'] == user_data[user_id]['first_channel']:
        first_id = user_data[user_id]['first_message_id']
        last_id = info['message_id']
        start_id = min(first_id, last_id)
        end_id = max(first_id, last_id)
        total_posts = end_id - start_id + 1
        
        user_data[user_id].update({
            "state": "ready",
            "last_url": last_url,
            "total_posts": total_posts
        })
        print(f"✅ حفظ الرابط الأخير: إجمالي {total_posts} منشور")
    
    # الخطوة 5: بدء التحليل
    if user_data[user_id]["state"] == "ready":
        print("✅ جاهز لبدء التحليل")
        return True
    
    return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار البوت البسيط")
    print("=" * 40)
    
    success = True
    
    # اختبار المكتبات
    if not test_telegram_import():
        success = False
    
    # اختبار رمز البوت
    if not test_bot_token():
        success = False
    
    # اختبار استخراج الروابط
    if not test_url_extraction():
        success = False
    
    # اختبار حساب العدد
    if not test_count_calculation():
        success = False
    
    # محاكاة تدفق المستخدم
    if not simulate_user_flow():
        success = False
    
    print("\n" + "=" * 40)
    
    if success:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n📋 الخطوات التالية:")
        print("1. شغل البوت: python simple_working_bot.py")
        print("2. أو استخدم: run_simple_bot.bat")
        print("3. ابحث عن البوت في تيليجرام")
        print("4. أرسل /start")
        print("5. جرب الروابط:")
        print("   - الأول: https://t.me/telegram/1")
        print("   - الأخير: https://t.me/telegram/10")
        print("   - النتيجة: 10 منشورات")
        
        print("\n✨ مميزات البوت البسيط:")
        print("- ✅ يعمل بدون مكتبات إضافية")
        print("- ✅ يقرأ الروابط بشكل صحيح")
        print("- ✅ يحسب العدد تلقائياً")
        print("- ✅ قائمة أوامر في الشات")
        print("- ✅ رسائل واضحة ومفيدة")
        
    else:
        print("❌ بعض الاختبارات فشلت")
        print("\n💡 نصائح:")
        print("- تأكد من تثبيت python-telegram-bot")
        print("- تحقق من اتصال الإنترنت")
    
    return success

if __name__ == "__main__":
    try:
        result = main()
        if not result:
            print("\n🔧 للمساعدة:")
            print("- python -m pip install python-telegram-bot")
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
    
    input("\nاضغط Enter للخروج...")
