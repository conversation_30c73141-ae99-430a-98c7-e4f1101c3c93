#!/usr/bin/env python3
"""
بوت تحليل المنشورات - النسخة الكاملة
"""

import logging
import re
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes, ConversationHandler

from config import BOT_TOKEN, SAVED_POSTS, SIMILARITY_THRESHOLD, API_ID, API_HASH
from utils import check_post_similarity
from telegram_reader import analyze_telegram_posts, TelegramReader

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# حالات المحادثة
WAITING_FIRST_URL, WAITING_LAST_URL = range(2)

# تخزين بيانات المستخدمين
user_data = {}

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج أمر /start"""
    keyboard = [
        [InlineKeyboardButton("📊 تقرير عرض النشر اليومي", callback_data='daily_report')],
        [InlineKeyboardButton("📢 تعميم آخر نشر", callback_data='latest_post')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    welcome_message = """🤖 مرحباً بك في بوت تحليل المنشورات!

اختر أحد الخيارات التالية:"""
    
    await update.message.reply_text(welcome_message, reply_markup=reply_markup)

async def button_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """معالج الأزرار"""
    query = update.callback_query
    await query.answer()
    
    user_id = query.from_user.id
    
    if query.data == 'daily_report':
        # بدء عملية تقرير النشر اليومي
        user_data[user_id] = {'mode': 'daily_report'}
        
        await query.edit_message_text(
            "📊 تقرير عرض النشر اليومي\n\n"
            "الرجاء إرسال رابط أول منشور:\n"
            "(مثال: https://t.me/channel/123)\n\n"
            "أو أرسل /cancel للإلغاء"
        )
        return WAITING_FIRST_URL
        
    elif query.data == 'latest_post':
        await query.edit_message_text(
            "📢 تعميم آخر نشر\n\n"
            "هذه الميزة قيد التطوير...\n\n"
            "أرسل /start للعودة للقائمة الرئيسية"
        )
        return ConversationHandler.END
    
    elif query.data == 'start_calculation':
        await start_calculation(update, context)
        return ConversationHandler.END
    
    return ConversationHandler.END

async def handle_first_url(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """معالج رابط أول منشور"""
    user_id = update.from_user.id
    url = update.message.text.strip()

    # التحقق من صحة الرابط
    if not re.match(r'https?://', url):
        await update.message.reply_text(
            "❌ الرجاء إرسال رابط صحيح يبدأ بـ http:// أو https://\n"
            "مثال: https://t.me/channel/123"
        )
        return WAITING_FIRST_URL

    # التحقق من أن الرابط من تيليجرام
    if not any(domain in url.lower() for domain in ['t.me', 'telegram.me', 'telegram.org']):
        await update.message.reply_text(
            "❌ حالياً البوت يدعم فقط روابط قنوات تيليجرام\n"
            "مثال: https://t.me/channel/123"
        )
        return WAITING_FIRST_URL

    # استخراج معلومات الرابط
    reader = TelegramReader()
    channel_info = reader.extract_channel_info(url)

    if not channel_info:
        await update.message.reply_text(
            "❌ رابط غير صحيح. تأكد من أن الرابط بالشكل:\n"
            "https://t.me/channel_name/message_id"
        )
        return WAITING_FIRST_URL

    # حفظ الرابط الأول
    if user_id not in user_data:
        user_data[user_id] = {}

    user_data[user_id]['first_url'] = url
    user_data[user_id]['first_channel'] = channel_info['channel']
    user_data[user_id]['first_message_id'] = channel_info['message_id']

    await update.message.reply_text(
        f"✅ تم حفظ رابط أول منشور\n"
        f"📍 القناة: @{channel_info['channel']}\n"
        f"📝 رقم المنشور: {channel_info['message_id']}\n\n"
        f"الآن الرجاء إرسال رابط آخر منشور من نفس القناة:\n"
        f"(مثال: https://t.me/{channel_info['channel']}/456)"
    )

    return WAITING_LAST_URL

async def handle_last_url(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """معالج رابط آخر منشور"""
    user_id = update.from_user.id
    url = update.message.text.strip()

    # التحقق من صحة الرابط
    if not re.match(r'https?://', url):
        await update.message.reply_text(
            "❌ الرجاء إرسال رابط صحيح يبدأ بـ http:// أو https://\n"
            "مثال: https://t.me/channel/456"
        )
        return WAITING_LAST_URL

    # التحقق من أن الرابط من تيليجرام
    if not any(domain in url.lower() for domain in ['t.me', 'telegram.me', 'telegram.org']):
        await update.message.reply_text(
            "❌ حالياً البوت يدعم فقط روابط قنوات تيليجرام\n"
            "مثال: https://t.me/channel/456"
        )
        return WAITING_LAST_URL

    # استخراج معلومات الرابط
    reader = TelegramReader()
    channel_info = reader.extract_channel_info(url)

    if not channel_info:
        await update.message.reply_text(
            "❌ رابط غير صحيح. تأكد من أن الرابط بالشكل:\n"
            "https://t.me/channel_name/message_id"
        )
        return WAITING_LAST_URL

    # التحقق من أن الرابط من نفس القناة
    first_channel = user_data[user_id].get('first_channel')
    if channel_info['channel'] != first_channel:
        await update.message.reply_text(
            f"❌ يجب أن يكون الرابط من نفس القناة: @{first_channel}\n"
            f"الرابط المرسل من قناة: @{channel_info['channel']}\n\n"
            f"الرجاء إرسال رابط من القناة الصحيحة:\n"
            f"https://t.me/{first_channel}/message_id"
        )
        return WAITING_LAST_URL

    # حساب عدد المنشورات
    first_message_id = user_data[user_id]['first_message_id']
    last_message_id = channel_info['message_id']

    # ترتيب المعرفات
    start_id = min(first_message_id, last_message_id)
    end_id = max(first_message_id, last_message_id)

    total_posts = end_id - start_id + 1

    # حفظ الرابط الأخير
    user_data[user_id]['last_url'] = url
    user_data[user_id]['last_channel'] = channel_info['channel']
    user_data[user_id]['last_message_id'] = channel_info['message_id']
    user_data[user_id]['total_posts'] = total_posts
    user_data[user_id]['start_id'] = start_id
    user_data[user_id]['end_id'] = end_id

    # إنشاء زر ابدأ الحساب
    keyboard = [[InlineKeyboardButton("🚀 ابدأ الحساب", callback_data='start_calculation')]]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await update.message.reply_text(
        f"✅ تم حفظ رابط آخر منشور\n\n"
        f"📊 معلومات التحليل:\n"
        f"📍 القناة: @{channel_info['channel']}\n"
        f"📝 من المنشور: {start_id}\n"
        f"📝 إلى المنشور: {end_id}\n"
        f"🔢 إجمالي المنشورات: {total_posts} منشور\n\n"
        f"اضغط على الزر أدناه لبدء عملية التحليل:",
        reply_markup=reply_markup
    )

    return ConversationHandler.END

async def start_calculation(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """بدء عملية الحساب والتحليل"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id

    if user_id not in user_data or 'first_url' not in user_data[user_id] or 'last_url' not in user_data[user_id]:
        await query.edit_message_text("❌ خطأ: لم يتم العثور على الروابط المطلوبة")
        return

    # الحصول على البيانات المحفوظة
    first_url = user_data[user_id]['first_url']
    last_url = user_data[user_id]['last_url']
    total_posts = user_data[user_id]['total_posts']
    channel = user_data[user_id]['first_channel']
    start_id = user_data[user_id]['start_id']
    end_id = user_data[user_id]['end_id']

    await query.edit_message_text(
        f"🔄 جاري تحليل {total_posts} منشور من قناة @{channel}...\n"
        f"📝 من المنشور {start_id} إلى {end_id}\n\n"
        f"الرجاء الانتظار..."
    )

    try:
        # تحليل المنشورات
        report = await analyze_posts_with_count(user_data[user_id])

        # إرسال التقرير (تقسيمه إذا كان طويلاً)
        if len(report) > 4000:
            # تقسيم التقرير إلى أجزاء
            parts = [report[i:i+4000] for i in range(0, len(report), 4000)]
            await query.edit_message_text(parts[0])
            for part in parts[1:]:
                await context.bot.send_message(chat_id=query.message.chat_id, text=part)
        else:
            await query.edit_message_text(report)

        # إضافة زر العودة للقائمة الرئيسية
        keyboard = [[InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data='back_to_main')]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await context.bot.send_message(
            chat_id=query.message.chat_id,
            text="أرسل /start لبدء تحليل جديد",
            reply_markup=reply_markup
        )

    except Exception as e:
        logger.error(f"خطأ في التحليل: {e}")
        await query.edit_message_text(f"❌ حدث خطأ أثناء التحليل:\n{str(e)}\n\nأرسل /start للمحاولة مرة أخرى")

async def analyze_posts_with_count(user_info: dict) -> str:
    """تحليل المنشورات مع عرض العدد والتفاصيل"""
    try:
        first_url = user_info['first_url']
        last_url = user_info['last_url']
        total_posts = user_info['total_posts']
        channel = user_info['first_channel']
        start_id = user_info['start_id']
        end_id = user_info['end_id']

        # استخدام قارئ تيليجرام للقنوات
        analysis = await analyze_telegram_posts(first_url, last_url)

        if not analysis['success']:
            return f"❌ خطأ في تحليل قناة تيليجرام: {analysis.get('error', 'خطأ غير معروف')}"

        # تحليل الرسائل المجلبة
        messages = analysis['messages']

        if not messages:
            return f"""❌ لم يتم العثور على رسائل في النطاق المحدد

📊 معلومات البحث:
• القناة: @{channel}
• النطاق: من {start_id} إلى {end_id}
• العدد المتوقع: {total_posts} منشور

💡 تأكد من أن:
- القناة عامة ومتاحة
- أرقام المنشورات صحيحة
- المنشورات موجودة فعلاً"""

        # تحليل المنشورات
        matching_posts = 0
        daily_report = []

        # تجميع الرسائل حسب التاريخ
        daily_messages = {}
        for msg in messages:
            date_str = msg['date'].strftime('%Y-%m-%d')
            if date_str not in daily_messages:
                daily_messages[date_str] = []
            daily_messages[date_str].append(msg)

        # فحص كل يوم
        for date_str in sorted(daily_messages.keys()):
            day_messages = daily_messages[date_str]
            matching_count = 0

            for msg in day_messages:
                if check_post_similarity(msg['content'], SAVED_POSTS, SIMILARITY_THRESHOLD):
                    matching_count += 1

            if matching_count > 0:
                matching_posts += matching_count
                if matching_count == 1:
                    daily_report.append(f"{date_str}: تم نشر منشور مطابق")
                else:
                    daily_report.append(f"{date_str}: تم نشر {matching_count} منشورات مطابقة")
            else:
                daily_report.append(f"{date_str}: لم يُنشر محتوى مطابق")

        days_without_posts = len([line for line in daily_report if "لم يُنشر" in line])

        # حساب النسب المئوية
        matching_percentage = (matching_posts / len(messages)) * 100 if messages else 0
        coverage_percentage = (len(messages) / total_posts) * 100 if total_posts > 0 else 0

        # إنشاء التقرير النهائي
        report = f"""📊 تقرير عرض النشر اليومي

📈 إحصائيات عامة:
• القناة: @{channel}
• النطاق: من المنشور {start_id} إلى {end_id}
• العدد المتوقع: {total_posts} منشور
• الرسائل المجلبة: {len(messages)} رسالة
• نسبة التغطية: {coverage_percentage:.1f}%

🎯 نتائج التحليل:
• المنشورات المطابقة: {matching_posts}
• نسبة التطابق: {matching_percentage:.1f}%
• الأيام بدون نشر مطابق: {days_without_posts}
• إجمالي الأيام: {len(daily_messages)}

📅 التقرير اليومي:
{chr(10).join(daily_report)}

✅ تم إنشاء التقرير بنجاح"""

        return report.strip()

    except Exception as e:
        logger.error(f"خطأ في تحليل المنشورات: {e}")
        return f"❌ حدث خطأ أثناء التحليل: {str(e)}"

async def analyze_posts(first_url: str, last_url: str) -> str:
    """تحليل المنشورات بين رابطين"""
    try:
        # التحقق من نوع الروابط
        is_telegram_url = any(domain in first_url.lower() for domain in ['t.me', 'telegram.me', 'telegram.org'])
        
        if is_telegram_url:
            # استخدام قارئ تيليجرام للقنوات
            analysis = await analyze_telegram_posts(first_url, last_url)
            
            if not analysis['success']:
                return f"❌ خطأ في تحليل قناة تيليجرام: {analysis.get('error', 'خطأ غير معروف')}"
            
            # تحليل الرسائل المجلبة
            messages = analysis['messages']
            matching_posts = 0
            daily_report = []
            
            if not messages:
                return "❌ لم يتم العثور على رسائل في النطاق المحدد"
            
            # تجميع الرسائل حسب التاريخ
            daily_messages = {}
            for msg in messages:
                date_str = msg['date'].strftime('%Y-%m-%d')
                if date_str not in daily_messages:
                    daily_messages[date_str] = []
                daily_messages[date_str].append(msg)
            
            # فحص كل يوم
            for date_str in sorted(daily_messages.keys()):
                day_messages = daily_messages[date_str]
                matching_count = 0
                
                for msg in day_messages:
                    if check_post_similarity(msg['content'], SAVED_POSTS, SIMILARITY_THRESHOLD):
                        matching_count += 1
                
                if matching_count > 0:
                    matching_posts += matching_count
                    if matching_count == 1:
                        daily_report.append(f"{date_str}: تم نشر منشور مطابق")
                    else:
                        daily_report.append(f"{date_str}: تم نشر {matching_count} منشورات مطابقة")
                else:
                    daily_report.append(f"{date_str}: لم يُنشر محتوى مطابق")
            
            days_without_posts = len([line for line in daily_report if "لم يُنشر" in line])
            
            # إنشاء التقرير النهائي
            report = f"""📊 تقرير عرض النشر اليومي

📈 إحصائيات عامة:
• القناة: @{analysis['channel']}
• إجمالي الرسائل: {analysis['total_messages']}
• المنشورات المطابقة: {matching_posts}
• الأيام بدون نشر مطابق: {days_without_posts}
• إجمالي الأيام: {len(daily_messages)}

📅 التقرير اليومي:
{chr(10).join(daily_report)}

✅ تم إنشاء التقرير بنجاح"""
            
        else:
            return "❌ حالياً البوت يدعم فقط روابط قنوات تيليجرام\nمثال: https://t.me/channel/123"
        
        return report.strip()
        
    except Exception as e:
        logger.error(f"خطأ في تحليل المنشورات: {e}")
        return f"❌ حدث خطأ أثناء التحليل: {str(e)}"

async def cancel(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """إلغاء المحادثة"""
    await update.message.reply_text(
        "تم إلغاء العملية.\n\nأرسل /start للبدء من جديد."
    )
    return ConversationHandler.END

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج أمر /help"""
    help_text = """🤖 بوت تحليل المنشورات

الأوامر المتاحة:
/start - بدء البوت وعرض القائمة الرئيسية
/help - عرض هذه المساعدة
/cancel - إلغاء العملية الحالية

المميزات:
📊 تقرير عرض النشر اليومي
📢 تعميم آخر نشر (قيد التطوير)

كيفية الاستخدام:
1. أرسل /start
2. اختر "تقرير عرض النشر اليومي"
3. أرسل رابط أول منشور من قناة تيليجرام
4. أرسل رابط آخر منشور من نفس القناة
5. سيحسب البوت عدد المنشورات بينهما
6. اضغط "ابدأ الحساب" لبدء التحليل

مثال على الاستخدام:
• الرابط الأول: https://t.me/telegram/1
• الرابط الأخير: https://t.me/telegram/100
• النتيجة: سيحلل 100 منشور

ملاحظات مهمة:
• يجب أن يكون الرابطان من نفس القناة
• القناة يجب أن تكون عامة
• البوت سيحسب العدد تلقائياً"""

    await update.message.reply_text(help_text)

async def set_bot_commands(application):
    """إعداد قائمة الأوامر للبوت"""
    from telegram import BotCommand

    commands = [
        BotCommand("start", "بدء البوت وعرض القائمة الرئيسية"),
        BotCommand("help", "عرض المساعدة والتعليمات"),
        BotCommand("cancel", "إلغاء العملية الحالية"),
    ]

    try:
        await application.bot.set_my_commands(commands)
        print("✅ تم إعداد قائمة الأوامر")
    except Exception as e:
        print(f"⚠️ فشل في إعداد قائمة الأوامر: {e}")

def main() -> None:
    """تشغيل البوت"""
    print("🤖 بدء تشغيل البوت الكامل...")
    print(f"🔑 رمز البوت: {BOT_TOKEN[:10]}...")
    print(f"🔧 API ID: {API_ID}")

    try:
        # إنشاء التطبيق
        application = Application.builder().token(BOT_TOKEN).build()

        # إعداد معالج المحادثة
        conv_handler = ConversationHandler(
            entry_points=[
                CallbackQueryHandler(button_handler, pattern='^daily_report$')
            ],
            states={
                WAITING_FIRST_URL: [MessageHandler(filters.TEXT & ~filters.COMMAND, handle_first_url)],
                WAITING_LAST_URL: [MessageHandler(filters.TEXT & ~filters.COMMAND, handle_last_url)],
            },
            fallbacks=[CommandHandler('cancel', cancel)],
            allow_reentry=True
        )

        # إضافة المعالجات بالترتيب الصحيح
        application.add_handler(CommandHandler('start', start))
        application.add_handler(CommandHandler('help', help_command))
        application.add_handler(CommandHandler('cancel', cancel))
        application.add_handler(conv_handler)
        application.add_handler(CallbackQueryHandler(button_handler, pattern='^latest_post$'))
        application.add_handler(CallbackQueryHandler(start_calculation, pattern='^start_calculation$'))

        # إعداد قائمة الأوامر
        async def post_init(application):
            await set_bot_commands(application)

        application.post_init = post_init

        print("✅ تم إعداد جميع المعالجات")
        print("🚀 البوت يعمل الآن...")
        print("💡 ابحث عن البوت في تيليجرام وأرسل /start")
        print("💡 اضغط Ctrl+C لإيقاف البوت")
        print("-" * 50)

        # تشغيل البوت
        application.run_polling(
            allowed_updates=Update.ALL_TYPES,
            drop_pending_updates=True
        )

    except KeyboardInterrupt:
        print("\n👋 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البوت: {e}")
        logger.error(f"خطأ في تشغيل البوت: {e}")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
