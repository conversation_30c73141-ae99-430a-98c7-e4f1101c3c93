#!/usr/bin/env python3
"""
اختبار API ID و API Hash
"""

import asyncio
import os

def check_config():
    """فحص ملف الإعدادات"""
    print("🔍 فحص ملف الإعدادات...")
    
    try:
        from config import API_ID, API_HASH, BOT_TOKEN
        
        # فحص BOT_TOKEN
        if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
            print("❌ BOT_TOKEN غير معد")
            return False
        else:
            print(f"✅ BOT_TOKEN معد: {BOT_TOKEN[:10]}...")
        
        # فحص API_ID
        if API_ID == "YOUR_API_ID_HERE":
            print("❌ API_ID غير معد")
            return False
        else:
            print(f"✅ API_ID معد: {API_ID}")
        
        # فحص API_HASH
        if API_HASH == "YOUR_API_HASH_HERE":
            print("❌ API_HASH غير معد")
            return False
        else:
            print(f"✅ API_HASH معد: {API_HASH[:10]}...")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد config.py: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في config.py: {e}")
        return False

def check_libraries():
    """فحص المكتبات المطلوبة"""
    print("\n📚 فحص المكتبات...")
    
    libraries = [
        ('pyrogram', 'pyrogram'),
        ('telegram', 'python-telegram-bot'),
        ('tgcrypto', 'tgcrypto')
    ]
    
    missing = []
    
    for lib_name, package_name in libraries:
        try:
            __import__(lib_name)
            print(f"✅ {package_name} متاح")
        except ImportError:
            print(f"❌ {package_name} غير متاح")
            missing.append(package_name)
    
    if missing:
        print(f"\n💡 قم بتثبيت المكتبات المفقودة:")
        for lib in missing:
            print(f"   python -m pip install {lib}")
        return False
    
    return True

async def test_pyrogram():
    """اختبار Pyrogram"""
    print("\n🔧 اختبار Pyrogram...")
    
    try:
        from pyrogram import Client
        from config import API_ID, API_HASH, BOT_TOKEN
        
        # إنشاء العميل
        client = Client(
            "test_session",
            api_id=API_ID,
            api_hash=API_HASH,
            bot_token=BOT_TOKEN
        )
        
        print("✅ تم إنشاء عميل Pyrogram")
        
        # اختبار الاتصال
        await client.start()
        print("✅ تم الاتصال بـ Telegram بنجاح")
        
        # جلب معلومات البوت
        me = await client.get_me()
        print(f"✅ معلومات البوت: {me.first_name} (@{me.username})")
        
        await client.stop()
        print("✅ تم إغلاق الاتصال")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في Pyrogram: {e}")
        return False

async def test_telegram_reader():
    """اختبار قارئ تيليجرام"""
    print("\n📖 اختبار قارئ تيليجرام...")
    
    try:
        from telegram_reader import TelegramReader
        
        reader = TelegramReader()
        
        if await reader.initialize():
            print("✅ تم تهيئة قارئ تيليجرام بنجاح")
            
            # اختبار استخراج معلومات القناة
            test_url = "https://t.me/telegram/123"
            channel_info = reader.extract_channel_info(test_url)
            
            if channel_info:
                print(f"✅ استخراج معلومات القناة: {channel_info}")
            else:
                print("❌ فشل في استخراج معلومات القناة")
            
            await reader.close()
            return True
        else:
            print("❌ فشل في تهيئة قارئ تيليجرام")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قارئ تيليجرام: {e}")
        return False

def cleanup_session_files():
    """تنظيف ملفات الجلسة"""
    session_files = [
        "test_session.session",
        "bot_session.session",
        "test_session.session-journal"
    ]
    
    for file in session_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"🗑️ تم حذف {file}")
            except:
                pass

async def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار API ID و API Hash")
    print("=" * 40)
    
    # تنظيف ملفات الجلسة القديمة
    cleanup_session_files()
    
    # فحص الإعدادات
    if not check_config():
        print("\n❌ يجب إعداد API ID و API Hash أولاً")
        print("📖 راجع ملف HOW_TO_GET_API_CREDENTIALS.md للتعليمات")
        return False
    
    # فحص المكتبات
    if not check_libraries():
        print("\n❌ يجب تثبيت المكتبات المطلوبة أولاً")
        return False
    
    # اختبار Pyrogram
    if not await test_pyrogram():
        print("\n❌ فشل اختبار Pyrogram")
        return False
    
    # اختبار قارئ تيليجرام
    if not await test_telegram_reader():
        print("\n❌ فشل اختبار قارئ تيليجرام")
        return False
    
    print("\n🎉 جميع الاختبارات نجحت!")
    print("\n📋 الخطوات التالية:")
    print("1. شغل البوت: python bot.py")
    print("2. جرب إرسال رابط قناة تيليجرام للبوت")
    print("3. مثال: https://t.me/telegram/123")
    
    # تنظيف ملفات الجلسة
    cleanup_session_files()
    
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if not result:
            print("\n💡 نصائح:")
            print("- تأكد من صحة API ID و API Hash")
            print("- تأكد من اتصال الإنترنت")
            print("- راجع ملف HOW_TO_GET_API_CREDENTIALS.md")
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
    
    input("\nاضغط Enter للخروج...")
