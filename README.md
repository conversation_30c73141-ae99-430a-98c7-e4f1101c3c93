# بوت تحليل المنشورات على تيليجرام

بوت تيليجرام لتحليل المنشورات وإنشاء تقارير يومية عن النشر.

## المميزات

### 1. تقرير عرض النشر اليومي
- يطلب من المستخدم رابط أول منشور ورابط آخر منشور
- يحلل جميع المنشورات بين الرابطين
- يقارن المنشورات مع المنشورات المحفوظة بنسبة تشابه ≥ 80%
- ينشئ تقريرًا يوميًا يوضح:
  - عدد المنشورات المطابقة
  - عدد الأيام بدون نشر
  - تفاصيل كل يوم (تم النشر أم لا)

### 2. تعميم آخر نشر
- ميزة قيد التطوير

## التثبيت والإعداد

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. إعداد البوت
1. أنشئ بوت جديد على تيليجرام عبر @BotFather
2. احصل على رمز البوت (Bot Token)
3. افتح ملف `config.py` وضع رمز البوت:
```python
BOT_TOKEN = "YOUR_BOT_TOKEN_HERE"
```

### 3. إعداد المنشورات المحفوظة
في ملف `config.py`، أضف المنشورات المحفوظة التي تريد المقارنة معها:
```python
SAVED_POSTS = [
    "نص المنشور الأول المحفوظ",
    "نص المنشور الثاني المحفوظ",
    # أضف المزيد...
]
```

### 4. تشغيل البوت
```bash
python bot.py
```

## كيفية الاستخدام

1. ابدأ محادثة مع البوت وأرسل `/start`
2. اختر "📊 تقرير عرض النشر اليومي"
3. أرسل رابط أول منشور
4. أرسل رابط آخر منشور
5. اضغط على "🚀 ابدأ الحساب"
6. انتظر التقرير

## هيكل المشروع

```
├── bot.py              # الملف الرئيسي للبوت
├── config.py           # إعدادات البوت
├── utils.py            # دوال مساعدة
├── requirements.txt    # المتطلبات
└── README.md          # هذا الملف
```

## ملاحظات مهمة

1. **استخراج المحتوى**: البوت يحاول استخراج محتوى المنشورات من الروابط المقدمة. قد تحتاج لتعديل دالة `extract_post_content` في `utils.py` حسب نوع الموقع المستخدم.

2. **استخراج التواريخ**: البوت يحاول استخراج تواريخ المنشورات من الروابط. قد تحتاج لتعديل دالة `get_post_date_from_url` حسب نوع الموقع.

3. **المنشورات المحفوظة**: يجب إضافة المنشورات المحفوظة في ملف `config.py` للمقارنة معها.

4. **نسبة التشابه**: يمكن تعديل نسبة التشابه المطلوبة في `config.py` (افتراضيًا 80%).

## التطوير المستقبلي

- تحسين استخراج المحتوى من مواقع مختلفة
- إضافة دعم لمنصات اجتماعية مختلفة
- تحسين خوارزمية حساب التشابه
- إضافة ميزة تعميم آخر نشر
- إضافة قاعدة بيانات لحفظ البيانات

## المساعدة

إذا واجهت أي مشاكل، تأكد من:
1. صحة رمز البوت
2. تثبيت جميع المتطلبات
3. صحة الروابط المرسلة
4. اتصال الإنترنت
