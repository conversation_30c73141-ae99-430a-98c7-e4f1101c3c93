#!/usr/bin/env python3
"""
اختبار النظام المصحح الذي يعمل بدون قارئ تيليجرام
"""

from datetime import datetime, timedelta
import random
from config import SAVED_POSTS

def calculate_similarity(text1: str, text2: str) -> float:
    """حساب التشابه بين نصين باستخدام difflib للدقة العالية"""
    import difflib
    
    # تنظيف النصوص
    text1 = text1.lower().strip()
    text2 = text2.lower().strip()
    
    # حساب التشابه باستخدام SequenceMatcher للدقة العالية
    similarity = difflib.SequenceMatcher(None, text1, text2).ratio()
    return similarity

def check_post_similarity(content: str, saved_posts: list, threshold: float = 0.8) -> bool:
    """فحص ما إذا كان المنشور يتشابه مع المنشورات المحفوظة بنسبة أعلى من 80%"""
    for saved_post in saved_posts:
        similarity = calculate_similarity(content, saved_post)
        if similarity > threshold:  # أعلى من 80%
            return True
    return False

def get_highest_similarity(content: str, saved_posts: list) -> float:
    """الحصول على أعلى نسبة تشابه مع المنشورات المحفوظة"""
    max_similarity = 0.0
    for saved_post in saved_posts:
        similarity = calculate_similarity(content, saved_post)
        max_similarity = max(max_similarity, similarity)
    return max_similarity

def calculate_realistic_date_from_message_id(message_id: int, start_id: int, end_id: int, 
                                           start_date: datetime, end_date: datetime) -> datetime:
    """حساب تاريخ واقعي بناءً على رقم المنشور والمدة الفعلية بين المنشورين"""
    
    # التأكد من ترتيب التواريخ
    if start_date > end_date:
        start_date, end_date = end_date, start_date
    
    # حساب المدة الإجمالية بالأيام
    total_days = (end_date - start_date).days
    total_posts = end_id - start_id + 1
    
    # حساب موقع المنشور الحالي في النطاق
    post_position = message_id - start_id
    
    # توزيع المنشورات على المدة الزمنية
    if total_posts > 1:
        day_interval = total_days / (total_posts - 1)
        days_offset = int(day_interval * post_position)
    else:
        days_offset = 0
    
    # حساب التاريخ المقدر للمنشور
    estimated_date = start_date + timedelta(days=days_offset)
    
    # إضافة وقت واقعي (بين 8 صباحاً و 10 مساءً)
    hour = random.randint(8, 22)
    minute = random.randint(0, 59)
    
    estimated_date = estimated_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
    
    return estimated_date

def test_realistic_system():
    """اختبار النظام الواقعي الجديد"""
    print("🧪 اختبار النظام المصحح")
    print("=" * 40)
    
    # محاكاة بيانات واقعية
    start_id = 100
    end_id = 150  # 51 منشور
    total_posts = end_id - start_id + 1
    
    # محاكاة مدة 32 يوم (كما في المثال)
    end_date = datetime.now() - timedelta(days=7)  # قبل أسبوع
    start_date = end_date - timedelta(days=32)     # قبل 32 يوم
    
    print(f"📊 البيانات الأساسية:")
    print(f"   • من المنشور: {start_id}")
    print(f"   • إلى المنشور: {end_id}")
    print(f"   • إجمالي المنشورات: {total_posts}")
    print(f"   • المدة الفعلية: {(end_date - start_date).days + 1} يوم")
    print(f"   • من تاريخ: {start_date.strftime('%Y-%m-%d')}")
    print(f"   • إلى تاريخ: {end_date.strftime('%Y-%m-%d')}")
    print()
    
    # محاكاة منشورات واقعية
    content_options = [
        "تداول مع إنزو دون الحاجة لإيداع أولي, حيث يمكنك الآن فتح حساباً حقيقياً والحصول على بونص ترحيبي بقيمة 30$ لتبدأ رحلتك الأستثمارية على الفور. فضلا على امكانية استخدامك لهذا البونص في صفقاتك, يمكنك سحب أرباحك بكل سهولة تداول مع أفضل وسيط STP موثوق ومرخص من أبرز الهيئات الرقابية في الشرق الأوسط، لضمان تجربة تداول آمنة وشفافة",  # مطابق 100%
        "إعلان مهم للمتابعين",  # مطابق 100%
        "تحديث جديد في الخدمات",  # مطابق 100%
        "خبر عاجل ومهم",  # مطابق 100%
        "منشور ترويجي للخدمات",  # مطابق 100%
        "معلومات مفيدة ونصائح",  # مطابق 100%
        "محتوى تعليمي مفيد",  # مطابق 100%
        "عرض خاص ومحدود",  # مطابق 100%
        # منشورات غير مطابقة
        "محتوى عادي غير مطابق نهائياً",
        "منشور عشوائي آخر لا يطابق",
        "نص غير مطابق تماماً مع أي شيء",
        "كلام عشوائي لا معنى له",
        "منشور إخباري عادي",
        "تحديث تقني لا يطابق",
        "إعلان تجاري مختلف",
        "محتوى ترفيهي عادي"
    ]
    
    # تحليل المنشورات
    matching_posts = []
    post_counter = 1
    
    print("🔍 تحليل المنشورات:")
    for message_id in range(start_id, end_id + 1):
        # اختيار محتوى بنسبة واقعية (حوالي 15-20% مطابق)
        if random.random() < 0.18:  # 18% احتمال أن يكون المنشور مطابق
            content = random.choice(content_options[:8])  # من المنشورات المطابقة
        else:
            content = random.choice(content_options[8:])  # من المنشورات غير المطابقة
        
        # فحص التطابق بنسبة أعلى من 80%
        if check_post_similarity(content, SAVED_POSTS, threshold=0.8):
            similarity = get_highest_similarity(content, SAVED_POSTS)
            
            # حساب التاريخ الواقعي للمنشور
            post_date = calculate_realistic_date_from_message_id(
                message_id, start_id, end_id, start_date, end_date
            )
            
            matching_posts.append({
                'number': post_counter,
                'message_id': message_id,
                'content': content,
                'similarity': similarity,
                'date': post_date
            })
            
            post_counter += 1
    
    print(f"📈 النتائج:")
    print(f"   • إجمالي المنشورات المفحوصة: {total_posts}")
    print(f"   • المنشورات المطابقة (> 80%): {len(matching_posts)}")
    print(f"   • نسبة المطابقة: {len(matching_posts)/total_posts*100:.1f}%")
    print()
    
    if matching_posts:
        print("📅 المنشورات المطابقة:")
        for post in matching_posts:
            similarity_percent = int(post['similarity'] * 100)
            print(f"   منشور رقم {post['number']}: {post['date'].strftime('%Y-%m-%d %H:%M')} (تشابه: {similarity_percent}%)")
        
        # تجميع حسب التاريخ
        daily_data = {}
        for post in matching_posts:
            date_str = post['date'].strftime('%Y-%m-%d')
            if date_str not in daily_data:
                daily_data[date_str] = []
            daily_data[date_str].append(post)
        
        print()
        print("📋 التقرير اليومي:")
        for date_str in sorted(daily_data.keys()):
            posts_count = len(daily_data[date_str])
            if posts_count == 1:
                post = daily_data[date_str][0]
                similarity_percent = int(post['similarity'] * 100)
                print(f"   {date_str}: المنشور رقم {post['number']} (تشابه: {similarity_percent}%)")
            else:
                print(f"   {date_str}: {posts_count} منشور")
        
        # حساب الإحصائيات النهائية
        all_dates = [datetime.strptime(date, '%Y-%m-%d').date() for date in daily_data.keys()]
        actual_start_date = min(all_dates)
        actual_end_date = max(all_dates)
        actual_days_range = (actual_end_date - actual_start_date).days + 1
        
        total_similarity = sum(post['similarity'] for post in matching_posts)
        avg_similarity = (total_similarity / len(matching_posts) * 100)
        
        print()
        print("📊 الإحصائيات النهائية:")
        print(f"   • المنشورات المطابقة: {len(matching_posts)} منشور")
        print(f"   • متوسط نسبة التشابه: {avg_similarity:.1f}%")
        print(f"   • الأيام التي تحتوي على منشورات مطابقة: {len(daily_data)} يوم")
        print(f"   • إجمالي الأيام في النطاق: {actual_days_range} يوم")
        print(f"   • المدة الفعلية: من {actual_start_date} إلى {actual_end_date}")
    
    else:
        print("❌ لا توجد منشورات مطابقة بنسبة أعلى من 80%")
    
    print()
    print("✅ انتهى الاختبار")

if __name__ == "__main__":
    test_realistic_system()
    input("\nاضغط Enter للخروج...")
