#!/usr/bin/env python3
"""
اختبار النظام مع البيانات الفعلية
"""

import asyncio
from datetime import datetime, timedelta
from config import SAVED_POSTS

def calculate_similarity(text1: str, text2: str) -> float:
    """حساب التشابه بين نصين باستخدام difflib للدقة العالية"""
    import difflib
    
    # تنظيف النصوص
    text1 = text1.lower().strip()
    text2 = text2.lower().strip()
    
    # حساب التشابه باستخدام SequenceMatcher للدقة العالية
    similarity = difflib.SequenceMatcher(None, text1, text2).ratio()
    return similarity

def check_post_similarity(content: str, saved_posts: list, threshold: float = 0.8) -> bool:
    """فحص ما إذا كان المنشور يتشابه مع المنشورات المحفوظة بنسبة أعلى من 80%"""
    for saved_post in saved_posts:
        similarity = calculate_similarity(content, saved_post)
        if similarity > threshold:  # أعلى من 80%
            return True
    return False

def get_highest_similarity(content: str, saved_posts: list) -> float:
    """الحصول على أعلى نسبة تشابه مع المنشورات المحفوظة"""
    max_similarity = 0.0
    for saved_post in saved_posts:
        similarity = calculate_similarity(content, saved_post)
        max_similarity = max(max_similarity, similarity)
    return max_similarity

async def test_telegram_reader():
    """اختبار قارئ تيليجرام"""
    print("🧪 اختبار قارئ تيليجرام")
    print("=" * 40)
    
    try:
        from telegram_reader import analyze_telegram_posts
        print("✅ قارئ تيليجرام متوفر")
        
        # اختبار روابط وهمية
        first_url = "https://t.me/telegram/1"
        last_url = "https://t.me/telegram/5"
        
        print(f"🔍 اختبار تحليل المنشورات:")
        print(f"   من: {first_url}")
        print(f"   إلى: {last_url}")
        
        # محاولة التحليل
        analysis = await analyze_telegram_posts(first_url, last_url)
        
        if analysis['success']:
            print("✅ تم جلب البيانات بنجاح")
            print(f"📊 إجمالي المنشورات: {analysis['total_messages']}")
            print(f"📅 من تاريخ: {analysis['start_date']}")
            print(f"📅 إلى تاريخ: {analysis['end_date']}")
            
            # تحليل المنشورات
            matching_count = 0
            for msg in analysis['messages']:
                if check_post_similarity(msg['content'], SAVED_POSTS, threshold=0.8):
                    matching_count += 1
            
            print(f"🎯 المنشورات المطابقة (> 80%): {matching_count}")
            
        else:
            print(f"❌ خطأ في التحليل: {analysis.get('error', 'خطأ غير معروف')}")
            
    except ImportError:
        print("❌ قارئ تيليجرام غير متوفر")
        print("💡 تأكد من:")
        print("   • وجود ملف telegram_reader.py")
        print("   • تثبيت pyrogram: pip install pyrogram")
        print("   • إعداد API_ID و API_HASH في config.py")
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")

def test_date_calculation():
    """اختبار حساب التواريخ الفعلية"""
    print("\n📅 اختبار حساب التواريخ الفعلية")
    print("=" * 40)
    
    # محاكاة بيانات فعلية
    start_date = datetime(2024, 7, 1, 10, 0)  # 1 يوليو
    end_date = datetime(2024, 8, 1, 15, 30)   # 1 أغسطس (31 يوم)
    
    print(f"📊 المدة الفعلية:")
    print(f"   من: {start_date.strftime('%Y-%m-%d %H:%M')}")
    print(f"   إلى: {end_date.strftime('%Y-%m-%d %H:%M')}")
    
    actual_days = (end_date.date() - start_date.date()).days + 1
    print(f"   المدة: {actual_days} يوم")
    
    # محاكاة منشورات مطابقة
    matching_posts = [
        {'date': datetime(2024, 7, 5, 12, 0), 'content': 'منشور 1'},
        {'date': datetime(2024, 7, 15, 14, 30), 'content': 'منشور 2'},
        {'date': datetime(2024, 7, 25, 16, 45), 'content': 'منشور 3'},
    ]
    
    print(f"\n🎯 المنشورات المطابقة:")
    for i, post in enumerate(matching_posts, 1):
        print(f"   منشور {i}: {post['date'].strftime('%Y-%m-%d %H:%M')}")
    
    # تجميع حسب التاريخ
    daily_data = {}
    for post in matching_posts:
        date_str = post['date'].strftime('%Y-%m-%d')
        if date_str not in daily_data:
            daily_data[date_str] = []
        daily_data[date_str].append(post)
    
    print(f"\n📋 التقرير اليومي:")
    for date_str in sorted(daily_data.keys()):
        posts_count = len(daily_data[date_str])
        print(f"   {date_str}: {posts_count} منشور")
    
    print(f"\n📈 الإحصائيات:")
    print(f"   • إجمالي المنشورات المطابقة: {len(matching_posts)}")
    print(f"   • الأيام التي تحتوي على منشورات: {len(daily_data)}")
    print(f"   • إجمالي الأيام في النطاق: {actual_days}")

def test_similarity_with_real_examples():
    """اختبار التشابه مع أمثلة واقعية"""
    print("\n🔍 اختبار التشابه مع أمثلة واقعية")
    print("=" * 40)
    
    # أمثلة واقعية للاختبار
    test_posts = [
        "تداول مع إنزو دون الحاجة لإيداع أولي, حيث يمكنك الآن فتح حساباً حقيقياً والحصول على بونص ترحيبي بقيمة 30$ لتبدأ رحلتك الأستثمارية على الفور",  # مطابق جزئياً
        "إعلان مهم للمتابعين",  # مطابق 100%
        "تحديث جديد في الخدمات",  # مطابق 100%
        "منشور عشوائي لا يطابق أي شيء",  # غير مطابق
        "خبر عاجل ومهم",  # مطابق 100%
    ]
    
    print("📊 نتائج التشابه:")
    for i, content in enumerate(test_posts, 1):
        similarity = get_highest_similarity(content, SAVED_POSTS)
        is_match = check_post_similarity(content, SAVED_POSTS, threshold=0.8)
        
        similarity_percent = int(similarity * 100)
        status = "✅ مطابق" if is_match else "❌ غير مطابق"
        
        print(f"   منشور {i}: {status} ({similarity_percent}%)")
        print(f"      النص: {content[:40]}...")

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار النظام مع البيانات الفعلية")
    print("=" * 50)
    
    # اختبار قارئ تيليجرام
    await test_telegram_reader()
    
    # اختبار حساب التواريخ
    test_date_calculation()
    
    # اختبار التشابه
    test_similarity_with_real_examples()
    
    print("\n✅ انتهى الاختبار")
    print("\n💡 ملاحظات:")
    print("• إذا كان قارئ تيليجرام متوفر، سيتم استخدام البيانات الفعلية")
    print("• إذا لم يكن متوفر، سيتم استخدام المحاكاة كبديل")
    print("• تأكد من إعداد API_ID و API_HASH في config.py للحصول على البيانات الفعلية")

if __name__ == "__main__":
    asyncio.run(main())
    input("\nاضغط Enter للخروج...")
