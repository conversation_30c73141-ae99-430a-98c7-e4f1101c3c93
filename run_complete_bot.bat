@echo off
chcp 65001 > nul
title بوت تحليل المنشورات - النسخة الكاملة
echo 🤖 بوت تحليل المنشورات - النسخة الكاملة
echo ===============================================
echo.

echo 🔍 فحص النظام...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متاح
    echo 💡 قم بتثبيت Python من https://python.org
    pause
    exit /b 1
)

echo ✅ Python متاح
echo.

echo 🧪 فحص API credentials...
python -c "from config import API_ID, API_HASH; print('✅ API credentials معدة' if API_ID != 'YOUR_API_ID_HERE' and API_HASH != 'YOUR_API_HASH_HERE' else '❌ API credentials غير معدة')" 2>nul
if errorlevel 1 (
    echo ❌ مشكلة في ملف config.py
    echo 💡 تأكد من إعداد API_ID و API_HASH
    pause
    exit /b 1
)

echo.
echo 🚀 تشغيل البوت الكامل...
echo 💡 ابحث عن البوت في تيليجرام وأرسل /start
echo 💡 اضغط Ctrl+C لإيقاف البوت
echo.
echo ⏳ بدء التشغيل...
echo.

python complete_bot.py

echo.
echo 👋 تم إيقاف البوت
pause
