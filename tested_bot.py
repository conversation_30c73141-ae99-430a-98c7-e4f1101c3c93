#!/usr/bin/env python3
"""
بوت مختبر ومضمون العمل
"""

import logging
import re
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, BotCommand
from telegram.ext import Application, <PERSON><PERSON><PERSON><PERSON>, Callback<PERSON>uery<PERSON><PERSON><PERSON>, MessageHandler, filters, ContextTypes

# إعداد التسجيل المفصل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# رمز البوت
BOT_TOKEN = "8415486238:AAFpoUZq_OXzZh74o8GrHmXXI7LwqPikSxI"

# المنشورات المحفوظة (يمكن تعديلها)
SAVED_POSTS = [
    "تداول مع إنزو دون الحاجة لإيداع أولي, حيث يمكنك الآن فتح حساباً حقيقياً والحصول على بونص ترحيبي بقيمة 30$ لتبدأ رحلتك الأستثمارية على الفور. فضلا على امكانية استخدامك لهذا البونص في صفقاتك, يمكنك سحب أرباحك بكل سهولة تداول مع أفضل وسيط STP موثوق ومرخص من أبرز الهيئات الرقابية في الشرق الأوسط، لضمان تجربة تداول آمنة وشفافة",
    "نص المنشور الثاني المحفوظ",
    "نص المنشور الثالث المحفوظ",
    "إعلان مهم",
    "تحديث جديد",
    "خبر عاجل",
    "منشور ترويجي",
    "معلومات مفيدة"
]

# قاموس لحفظ بيانات المستخدمين
users = {}

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """بدء البوت"""
    user_id = update.effective_user.id
    logger.info(f"المستخدم {user_id} بدأ البوت")
    
    # إعادة تعيين بيانات المستخدم
    users[user_id] = {
        "step": "main_menu",
        "first_url": None,
        "last_url": None,
        "first_info": None,
        "last_info": None
    }
    
    keyboard = [
        [InlineKeyboardButton("📊 تقرير عرض النشر اليومي", callback_data='start_report')],
        [InlineKeyboardButton("📢 تعميم آخر نشر", callback_data='latest_post')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await update.message.reply_text(
        "🤖 مرحباً بك في بوت تحليل المنشورات!\n\nاختر أحد الخيارات:",
        reply_markup=reply_markup
    )

async def button_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج الأزرار"""
    query = update.callback_query
    await query.answer()
    
    user_id = query.from_user.id
    logger.info(f"المستخدم {user_id} ضغط على: {query.data}")
    
    if query.data == 'start_report':
        users[user_id] = {
            "step": "waiting_first_url",
            "first_url": None,
            "last_url": None,
            "first_info": None,
            "last_info": None
        }
        
        await query.edit_message_text(
            f"📊 تقرير عرض النشر اليومي\n\n"
            f"📋 المنشورات المحفوظة للمقارنة: {len(SAVED_POSTS)} منشور\n\n"
            f"🔗 الرجاء إرسال رابط أول منشور:\n"
            f"مثال: https://t.me/telegram/1\n\n"
            f"أو أرسل /cancel للإلغاء"
        )
        
    elif query.data == 'latest_post':
        await query.edit_message_text(
            "📢 تعميم آخر نشر\n\n"
            "هذه الميزة قيد التطوير...\n\n"
            "أرسل /start للعودة"
        )
        
    elif query.data == 'calculate':
        await calculate_posts(update, context)

def extract_telegram_link(url: str):
    """استخراج معلومات من رابط تيليجرام"""
    logger.info(f"استخراج معلومات من: {url}")
    
    # أنماط الروابط المختلفة
    patterns = [
        r'(?:https?://)?(?:www\.)?t\.me/([^/\s]+)/(\d+)',
        r'(?:https?://)?(?:www\.)?telegram\.me/([^/\s]+)/(\d+)',
        r'(?:https?://)?(?:www\.)?telegram\.org/([^/\s]+)/(\d+)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url.strip())
        if match:
            channel = match.group(1)
            message_id = int(match.group(2))
            result = {
                'channel': channel,
                'message_id': message_id,
                'url': url.strip()
            }
            logger.info(f"تم استخراج: {result}")
            return result
    
    logger.warning(f"فشل في استخراج معلومات من: {url}")
    return None

async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج الرسائل النصية"""
    user_id = update.effective_user.id
    message_text = update.message.text.strip()
    
    logger.info(f"رسالة من {user_id}: {message_text}")
    
    # التحقق من وجود المستخدم في النظام
    if user_id not in users:
        logger.warning(f"المستخدم {user_id} غير موجود في النظام")
        await update.message.reply_text("أرسل /start للبدء")
        return
    
    user_step = users[user_id]["step"]
    logger.info(f"خطوة المستخدم {user_id}: {user_step}")
    
    if user_step == "waiting_first_url":
        await handle_first_url(update, context, message_text)
    elif user_step == "waiting_last_url":
        await handle_last_url(update, context, message_text)
    else:
        await update.message.reply_text("أرسل /start للبدء")

async def handle_first_url(update: Update, context: ContextTypes.DEFAULT_TYPE, url: str) -> None:
    """معالج الرابط الأول"""
    user_id = update.effective_user.id
    logger.info(f"معالجة الرابط الأول للمستخدم {user_id}: {url}")
    
    # التحقق من أنه رابط
    if not ('t.me' in url or 'telegram.me' in url or 'telegram.org' in url):
        await update.message.reply_text(
            "❌ هذا ليس رابط تيليجرام صحيح\n\n"
            "مثال صحيح: https://t.me/telegram/1\n"
            "أو: t.me/channel/123"
        )
        return
    
    # استخراج المعلومات
    link_info = extract_telegram_link(url)
    if not link_info:
        await update.message.reply_text(
            "❌ لا يمكن قراءة هذا الرابط\n\n"
            "تأكد من أن الرابط بالشكل:\n"
            "https://t.me/channel_name/message_number"
        )
        return
    
    # حفظ البيانات
    users[user_id]["first_url"] = url
    users[user_id]["first_info"] = link_info
    users[user_id]["step"] = "waiting_last_url"
    
    await update.message.reply_text(
        f"✅ تم حفظ الرابط الأول بنجاح!\n\n"
        f"📍 القناة: @{link_info['channel']}\n"
        f"📝 رقم المنشور: {link_info['message_id']}\n\n"
        f"🔗 الآن أرسل رابط آخر منشور من نفس القناة:\n"
        f"مثال: https://t.me/{link_info['channel']}/456"
    )

async def handle_last_url(update: Update, context: ContextTypes.DEFAULT_TYPE, url: str) -> None:
    """معالج الرابط الأخير"""
    user_id = update.effective_user.id
    logger.info(f"معالجة الرابط الأخير للمستخدم {user_id}: {url}")
    
    # التحقق من أنه رابط
    if not ('t.me' in url or 'telegram.me' in url or 'telegram.org' in url):
        await update.message.reply_text(
            "❌ هذا ليس رابط تيليجرام صحيح\n\n"
            "مثال صحيح: https://t.me/telegram/10"
        )
        return
    
    # استخراج المعلومات
    link_info = extract_telegram_link(url)
    if not link_info:
        await update.message.reply_text(
            "❌ لا يمكن قراءة هذا الرابط\n\n"
            "تأكد من أن الرابط بالشكل:\n"
            "https://t.me/channel_name/message_number"
        )
        return
    
    # التحقق من أن القناة نفسها
    first_channel = users[user_id]["first_info"]["channel"]
    if link_info["channel"] != first_channel:
        await update.message.reply_text(
            f"❌ يجب أن يكون الرابط من نفس القناة!\n\n"
            f"القناة الأولى: @{first_channel}\n"
            f"القناة الحالية: @{link_info['channel']}\n\n"
            f"أرسل رابط من @{first_channel}"
        )
        return
    
    # حساب العدد
    first_id = users[user_id]["first_info"]["message_id"]
    last_id = link_info["message_id"]
    start_id = min(first_id, last_id)
    end_id = max(first_id, last_id)
    total_posts = end_id - start_id + 1
    
    # حفظ البيانات
    users[user_id]["last_url"] = url
    users[user_id]["last_info"] = link_info
    users[user_id]["step"] = "ready_to_calculate"
    
    # إنشاء زر الحساب
    keyboard = [[InlineKeyboardButton("🚀 ابدأ الحساب", callback_data='calculate')]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await update.message.reply_text(
        f"✅ تم حفظ الرابط الأخير بنجاح!\n\n"
        f"📊 ملخص التحليل:\n"
        f"📍 القناة: @{link_info['channel']}\n"
        f"📝 من المنشور: {start_id}\n"
        f"📝 إلى المنشور: {end_id}\n"
        f"🔢 إجمالي المنشورات: {total_posts} منشور\n\n"
        f"اضغط الزر لبدء التحليل:",
        reply_markup=reply_markup
    )

def calculate_similarity(text1: str, text2: str) -> float:
    """حساب نسبة التشابه بين نصين"""
    import difflib

    # تنظيف النصوص
    text1 = text1.lower().strip()
    text2 = text2.lower().strip()

    # حساب التشابه
    similarity = difflib.SequenceMatcher(None, text1, text2).ratio()
    return similarity

def check_post_similarity(post_content: str, saved_posts: list, threshold: float = 0.8) -> bool:
    """فحص ما إذا كان المنشور يتشابه مع المنشورات المحفوظة"""
    for saved_post in saved_posts:
        similarity = calculate_similarity(post_content, saved_post)
        if similarity >= threshold:
            return True
    return False

def generate_mock_posts(start_id: int, end_id: int, channel: str) -> list:
    """إنشاء منشورات وهمية للمحاكاة"""
    from datetime import datetime, timedelta
    import random

    posts = []
    current_date = datetime.now() - timedelta(days=(end_id - start_id))

    for post_id in range(start_id, end_id + 1):
        # محاكاة وجود منشور (80% احتمال)
        if random.random() < 0.8:
            # اختيار محتوى عشوائي
            content_options = [
                "منشور إعلاني عن التداول والاستثمار",
                "تحديث جديد في الخدمات",
                "إعلان مهم للمتابعين",
                "معلومات مفيدة ونصائح",
                "خبر عاجل ومهم",
                "منشور ترويجي للخدمات",
                "تداول مع إنزو دون الحاجة لإيداع أولي",
                "محتوى تعليمي مفيد"
            ]

            content = random.choice(content_options)

            posts.append({
                'id': post_id,
                'date': current_date,
                'content': content,
                'url': f"https://t.me/{channel}/{post_id}"
            })

        # الانتقال لليوم التالي (أحياناً)
        if random.random() < 0.3:  # 30% احتمال تغيير اليوم
            current_date += timedelta(days=1)

    return posts

async def calculate_posts(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """حساب وعرض التقرير المفصل"""
    query = update.callback_query
    user_id = query.from_user.id

    logger.info(f"بدء الحساب للمستخدم {user_id}")

    if user_id not in users or users[user_id]["step"] != "ready_to_calculate":
        await query.edit_message_text("❌ خطأ: البيانات غير مكتملة")
        return

    user_data = users[user_id]
    first_info = user_data["first_info"]
    last_info = user_data["last_info"]

    first_id = first_info["message_id"]
    last_id = last_info["message_id"]
    start_id = min(first_id, last_id)
    end_id = max(first_id, last_id)
    total_posts = end_id - start_id + 1

    await query.edit_message_text(
        f"🔄 جاري تحليل {total_posts} منشور من @{first_info['channel']}...\n"
        f"من المنشور {start_id} إلى {end_id}\n\n"
        f"الرجاء الانتظار..."
    )

    # محاكاة وقت المعالجة
    import asyncio
    await asyncio.sleep(2)

    # إنشاء منشورات وهمية للمحاكاة
    posts = generate_mock_posts(start_id, end_id, first_info['channel'])

    # تحليل المنشورات
    matching_posts = 0
    daily_report = []
    days_with_posts = 0
    days_without_posts = 0

    # تجميع المنشورات حسب التاريخ
    daily_posts = {}
    for post in posts:
        date_str = post['date'].strftime('%Y-%m-%d')
        if date_str not in daily_posts:
            daily_posts[date_str] = []
        daily_posts[date_str].append(post)

    # إنشاء قائمة بجميع التواريخ في النطاق
    from datetime import datetime, timedelta
    start_date = min(post['date'] for post in posts) if posts else datetime.now()
    end_date = max(post['date'] for post in posts) if posts else datetime.now()

    current_date = start_date.date()
    end_date = end_date.date()

    post_counter = 1  # عداد المنشورات (الرابط الأول = منشور رقم 1)

    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')

        if date_str in daily_posts:
            day_posts = daily_posts[date_str]
            matching_count = 0

            # فحص كل منشور في هذا اليوم
            for post in day_posts:
                if check_post_similarity(post['content'], SAVED_POSTS, 0.3):  # نسبة أقل للمحاكاة
                    matching_count += 1

            if matching_count > 0:
                matching_posts += matching_count
                if len(day_posts) == 1:
                    daily_report.append(f"{date_str}: تم نشر منشور رقم {post_counter}")
                else:
                    daily_report.append(f"{date_str}: تم نشر {len(day_posts)} منشورات (بدءاً من رقم {post_counter})")
                days_with_posts += 1
                post_counter += len(day_posts)
            else:
                daily_report.append(f"{date_str}: لم يُنشر")
                days_without_posts += 1
        else:
            daily_report.append(f"{date_str}: لم يُنشر")
            days_without_posts += 1

        current_date += timedelta(days=1)

    # إنشاء التقرير المفصل
    report = f"""📊 تقرير عرض النشر اليومي

📈 إحصائيات عامة:
• القناة: @{first_info['channel']}
• النطاق: من المنشور {start_id} إلى {end_id}
• إجمالي المنشورات المتوقعة: {total_posts} منشور
• المنشورات الموجودة فعلياً: {len(posts)} منشور
• المنشورات المحفوظة للمقارنة: {len(SAVED_POSTS)} منشور

🎯 نتائج التحليل:
• المنشورات المطابقة: {matching_posts}
• الأيام التي تم النشر بها: {days_with_posts} يوم
• الأيام التي لم يُنشر بها: {days_without_posts} يوم
• إجمالي الأيام: {days_with_posts + days_without_posts} يوم

📅 التقرير اليومي:
{chr(10).join(daily_report)}

✅ تم إنشاء التقرير بنجاح!"""

    # إرسال التقرير (تقسيمه إذا كان طويلاً)
    if len(report) > 4000:
        parts = [report[i:i+4000] for i in range(0, len(report), 4000)]
        await query.edit_message_text(parts[0])
        for part in parts[1:]:
            await context.bot.send_message(chat_id=query.message.chat_id, text=part)
    else:
        await query.edit_message_text(report)

    # إعادة تعيين البيانات
    users[user_id]["step"] = "main_menu"

    # زر العودة
    keyboard = [[InlineKeyboardButton("🔙 بدء تحليل جديد", callback_data='start_report')]]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await context.bot.send_message(
        chat_id=query.message.chat_id,
        text="أرسل /start لبدء تحليل جديد",
        reply_markup=reply_markup
    )

async def cancel(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """إلغاء العملية"""
    user_id = update.effective_user.id
    users[user_id] = {"step": "main_menu"}
    
    await update.message.reply_text("تم إلغاء العملية. أرسل /start للبدء من جديد.")

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """المساعدة"""
    help_text = """🤖 بوت تحليل المنشورات

الأوامر:
/start - بدء البوت
/help - المساعدة
/cancel - إلغاء العملية

كيفية الاستخدام:
1. أرسل /start
2. اختر "تقرير عرض النشر اليومي"
3. أرسل رابط أول منشور
4. أرسل رابط آخر منشور
5. اضغط "ابدأ الحساب"

أمثلة على الروابط:
• https://t.me/telegram/1
• t.me/durov/123
• telegram.me/channel/456"""
    
    await update.message.reply_text(help_text)

async def set_commands(app):
    """إعداد قائمة الأوامر"""
    commands = [
        BotCommand("start", "بدء البوت"),
        BotCommand("help", "المساعدة"),
        BotCommand("cancel", "إلغاء العملية")
    ]
    
    try:
        await app.bot.set_my_commands(commands)
        logger.info("تم إعداد قائمة الأوامر")
    except Exception as e:
        logger.error(f"فشل في إعداد الأوامر: {e}")

def main():
    """تشغيل البوت"""
    print("🤖 بدء تشغيل البوت المختبر...")
    print(f"🔑 رمز البوت: {BOT_TOKEN[:10]}...")
    
    try:
        # إنشاء التطبيق
        app = Application.builder().token(BOT_TOKEN).build()
        
        # إضافة المعالجات
        app.add_handler(CommandHandler('start', start))
        app.add_handler(CommandHandler('help', help_command))
        app.add_handler(CommandHandler('cancel', cancel))
        app.add_handler(CallbackQueryHandler(button_handler))
        app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))
        
        # إعداد الأوامر
        app.post_init = set_commands
        
        print("✅ تم إعداد جميع المعالجات")
        print("🚀 البوت يعمل الآن...")
        print("💡 ابحث عن البوت وأرسل /start")
        print("💡 اضغط Ctrl+C للإيقاف")
        print("-" * 50)
        
        # تشغيل البوت
        app.run_polling(drop_pending_updates=True)
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف البوت")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        logger.error(f"خطأ في تشغيل البوت: {e}")

if __name__ == '__main__':
    main()
