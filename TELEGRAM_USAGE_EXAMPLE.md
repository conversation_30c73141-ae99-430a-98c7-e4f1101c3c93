# مثال على الاستخدام مع قنوات تيليجرام

## 🎯 الميزة الجديدة

البوت الآن يدعم قراءة المنشورات من قنوات تيليجرام العامة مباشرة! لا تحتاج لأن تكون مشرفاً في القناة.

## 📋 متطلبات الاستخدام

### 1. إعداد API credentials
- API ID و API Hash من https://my.telegram.org
- راجع ملف `HOW_TO_GET_API_CREDENTIALS.md` للتفاصيل

### 2. تثبيت المكتبات الإضافية
```bash
python -m pip install pyrogram tgcrypto
```

## 🔗 أنواع الروابط المدعومة

### روابط تيليجرام (مدعومة بالكامل):
```
https://t.me/channel_name/123
https://t.me/channel_name/456
```

### روابط المواقع العادية (دعم محدود):
```
https://example.com/post1
https://example.com/post2
```

## 📱 مثال على الاستخدام

### السيناريو:
تريد تحليل المنشورات في قناة أخبار من منشور رقم 100 إلى منشور رقم 200.

### الخطوات:

#### 1. بدء المحادثة
```
المستخدم: /start
البوت: 🤖 مرحباً بك في بوت تحليل المنشورات!

اختر أحد الخيارات التالية:
[📊 تقرير عرض النشر اليومي] [📢 تعميم آخر نشر]
```

#### 2. اختيار تقرير النشر اليومي
```
المستخدم: [يضغط على 📊 تقرير عرض النشر اليومي]
البوت: 📊 تقرير عرض النشر اليومي

الرجاء إرسال رابط أول منشور:
```

#### 3. إرسال رابط أول منشور (تيليجرام)
```
المستخدم: https://t.me/news_channel/100
البوت: ✅ تم حفظ رابط أول منشور

الآن الرجاء إرسال رابط آخر منشور:
```

#### 4. إرسال رابط آخر منشور
```
المستخدم: https://t.me/news_channel/200
البوت: ✅ تم حفظ رابط آخر منشور

اضغط على الزر أدناه لبدء عملية التحليل:
[🚀 ابدأ الحساب]
```

#### 5. بدء التحليل
```
المستخدم: [يضغط على 🚀 ابدأ الحساب]
البوت: 🔄 جاري تحليل المنشورات... الرجاء الانتظار
```

#### 6. النتيجة (مثال)
```
البوت: 📊 تقرير عرض النشر اليومي

📈 إحصائيات عامة:
• القناة: @news_channel
• إجمالي الرسائل: 101
• المنشورات المطابقة: 25
• الأيام بدون نشر مطابق: 8
• إجمالي الأيام: 15

📅 التقرير اليومي:
2025-01-01: تم نشر 3 منشورات مطابقة
2025-01-02: تم نشر منشور مطابق
2025-01-03: لم يُنشر محتوى مطابق
2025-01-04: تم نشر 2 منشورات مطابقة
2025-01-05: تم نشر منشور مطابق
2025-01-06: لم يُنشر محتوى مطابق
2025-01-07: تم نشر منشور مطابق
2025-01-08: تم نشر 4 منشورات مطابقة
2025-01-09: لم يُنشر محتوى مطابق
2025-01-10: تم نشر منشور مطابق
2025-01-11: تم نشر 2 منشورات مطابقة
2025-01-12: لم يُنشر محتوى مطابق
2025-01-13: تم نشر منشور مطابق
2025-01-14: تم نشر 3 منشورات مطابقة
2025-01-15: تم نشر منشور مطابق

✅ تم إنشاء التقرير بنجاح
```

## 🔍 كيف يعمل التحليل

### 1. جلب المنشورات:
- البوت يجلب جميع المنشورات بين الرقمين المحددين
- يقرأ النص والتسميات التوضيحية
- يجمع المنشورات حسب التاريخ

### 2. المقارنة:
- يقارن كل منشور مع المنشورات المحفوظة في `config.py`
- يستخدم خوارزمية التشابه النصي
- النسبة الافتراضية للتطابق: 80%

### 3. التقرير:
- يجمع النتائج حسب التاريخ
- يحسب الإحصائيات
- يعرض تقريراً يومياً مفصلاً

## ⚙️ الإعدادات المتقدمة

### تخصيص المنشورات المحفوظة:
```python
# في config.py
SAVED_POSTS = [
    "أخبار مهمة",
    "إعلان رسمي", 
    "تحديث جديد",
    "بيان صحفي",
    # أضف المزيد...
]
```

### تخصيص نسبة التشابه:
```python
# في config.py
SIMILARITY_THRESHOLD = 0.8  # 80% تشابه (افتراضي)
# SIMILARITY_THRESHOLD = 0.6  # 60% تشابه (أكثر مرونة)
# SIMILARITY_THRESHOLD = 0.9  # 90% تشابه (أكثر صرامة)
```

## 🚫 القيود والملاحظات

### القنوات المدعومة:
- ✅ القنوات العامة فقط
- ❌ القنوات الخاصة تحتاج دعوة
- ❌ المجموعات الخاصة غير مدعومة

### حدود المعدل:
- تيليجرام لديه حدود على عدد الطلبات
- البوت يتعامل مع هذا تلقائياً
- قد يحتاج وقت أطول للقنوات الكبيرة

### دقة التحليل:
- تعتمد على جودة المنشورات المحفوظة
- كلما كانت أكثر تنوعاً، كانت النتائج أدق
- يمكن تعديل نسبة التشابه حسب الحاجة

## 💡 نصائح للاستخدام الأمثل

### 1. اختيار المنشورات المحفوظة:
- استخدم أمثلة متنوعة من المحتوى المطلوب
- تجنب النصوص القصيرة جداً
- أضف كلمات مفتاحية مهمة

### 2. اختيار نطاق التحليل:
- ابدأ بنطاق صغير للاختبار
- تجنب النطاقات الكبيرة جداً (أكثر من 1000 منشور)
- استخدم منشورات من نفس القناة

### 3. تفسير النتائج:
- "منشور مطابق" = تشابه ≥ النسبة المحددة
- "لم يُنشر محتوى مطابق" = لا يوجد تشابه كافٍ
- التواريخ تعتمد على توقيت النشر الأصلي

## 🔧 استكشاف الأخطاء

### "خطأ في تحليل قناة تيليجرام":
- تحقق من صحة الروابط
- تأكد أن القناة عامة
- تحقق من API credentials

### "القناة خاصة أو غير متاحة":
- القناة تحتاج دعوة للانضمام
- أو القناة محذوفة/معطلة

### "فشل في تهيئة قارئ تيليجرام":
- تحقق من API ID و API Hash
- تحقق من اتصال الإنترنت
- شغل `python test_api.py` للتشخيص
