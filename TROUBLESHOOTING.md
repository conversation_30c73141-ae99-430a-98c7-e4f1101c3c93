# 🔧 دليل استكشاف الأخطاء

## المشاكل الشائعة وحلولها

### 1. البوت لا يستجيب في تيليجرام

#### الأسباب المحتملة:
- ❌ رمز البوت خاطئ
- ❌ مشكلة في الاتصال بالإنترنت  
- ❌ البوت لم يبدأ بشكل صحيح
- ❌ المكتبات غير مثبتة

#### الحلول:
```bash
# 1. اختبار سريع
python quick_test.py

# 2. تشخيص شامل
python debug_bot.py

# 3. تشغيل البوت المبسط
python minimal_bot.py
```

### 2. خطأ "ModuleNotFoundError: No module named 'telegram'"

#### الحل:
```bash
python -m pip install python-telegram-bot
```

### 3. خطأ في رمز البوت

#### التحقق من الرمز:
1. افتح `config.py`
2. تأكد أن `BOT_TOKEN` يحتوي على الرمز الصحيح
3. الرمز يجب أن يكون مثل: `"1234567890:ABCdefGHIjklMNOpqrsTUVwxyz"`

#### الحصول على رمز جديد:
1. اذهب إلى @BotFather في تيليجرام
2. أرسل `/mybots`
3. اختر البوت
4. اختر "API Token"

### 4. البوت يبدأ لكن لا يستجيب للأوامر

#### التحقق:
```bash
# تشغيل البوت مع رسائل التشخيص
python minimal_bot.py
```

#### إذا ظهرت رسالة "البوت يعمل الآن":
1. ابحث عن البوت في تيليجرام
2. أرسل `/start`
3. انتظر الرد

### 5. خطأ "python غير معروف"

#### الحل:
1. تأكد من تثبيت Python من https://python.org
2. أعد تشغيل Command Prompt
3. جرب `py` بدلاً من `python`

### 6. خطأ في تثبيت المكتبات

#### الحل:
```bash
# تحديث pip أولاً
python -m pip install --upgrade pip

# تثبيت المكتبات واحدة تلو الأخرى
python -m pip install python-telegram-bot
python -m pip install requests
python -m pip install beautifulsoup4
python -m pip install python-dateutil
```

## 🧪 أدوات التشخيص

### الاختبار السريع:
```bash
python quick_test.py
```

### التشخيص الشامل:
```bash
python debug_bot.py
```

### البوت المبسط للاختبار:
```bash
python minimal_bot.py
```

### التشخيص التلقائي (Windows):
```bash
diagnose.bat
```

## 📋 قائمة التحقق

### قبل تشغيل البوت:
- [ ] Python مثبت (3.8 أو أحدث)
- [ ] المكتبات مثبتة (`python -m pip install -r requirements.txt`)
- [ ] رمز البوت معد في `config.py`
- [ ] اتصال الإنترنت متاح

### عند تشغيل البوت:
- [ ] لا توجد رسائل خطأ في التيرمينال
- [ ] ظهور رسالة "البوت يعمل الآن"
- [ ] البوت يرد على `/start` في تيليجرام

### إذا لم يعمل البوت:
1. شغل `python quick_test.py`
2. إذا فشل الاختبار، اتبع التعليمات
3. إذا نجح الاختبار، شغل `python minimal_bot.py`
4. إذا لم يعمل البوت المبسط، شغل `python debug_bot.py`

## 🆘 طلب المساعدة

إذا استمرت المشاكل، قم بما يلي:

1. شغل `python debug_bot.py`
2. انسخ رسائل الخطأ
3. تحقق من:
   - إصدار Python
   - رمز البوت
   - رسائل الخطأ في التيرمينال

## 💡 نصائح إضافية

### لتشغيل البوت في الخلفية:
```bash
# Windows
start /min python bot.py

# أو استخدم
pythonw bot.py
```

### لإيقاف البوت:
- اضغط `Ctrl + C` في التيرمينال
- أو أغلق نافذة التيرمينال

### لمراقبة أداء البوت:
- راقب رسائل التيرمينال
- تحقق من استجابة البوت في تيليجرام
- استخدم `/status` للتحقق من حالة البوت
