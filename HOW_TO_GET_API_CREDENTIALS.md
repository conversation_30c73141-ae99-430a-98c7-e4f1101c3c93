# كيفية الحصول على API ID و API Hash

## لماذا نحتاج API ID و API Hash؟

API ID و API Hash مطلوبان لقراءة المنشورات من قنوات تيليجرام العامة بدون أن يكون البوت مشرفاً فيها. هذا يتيح للبوت:

- ✅ قراءة المنشورات من أي قناة عامة
- ✅ تحليل المنشورات بين تاريخين محددين
- ✅ العمل مع روابط t.me مباشرة

## الخطوات للحصول على API Credentials:

### 1. الذهاب إلى موقع Telegram API
اذهب إلى: https://my.telegram.org

### 2. تسجيل الدخول
- أدخل رقم هاتفك (نفس الرقم المستخدم في تيليجرام)
- ستصلك رسالة تحقق على تيليجرام
- أدخل كود التحقق

### 3. إنشاء تطبيق جديد
- اضغط على "API development tools"
- املأ النموذج:
  - **App title**: اسم التطبيق (مثل: "بوت تحليل المنشورات")
  - **Short name**: اسم مختصر (مثل: "posts_bot")
  - **URL**: يمكن تركه فارغ أو وضع أي رابط
  - **Platform**: اختر "Desktop"
  - **Description**: وصف مختصر (اختياري)

### 4. الحصول على البيانات
بعد إنشاء التطبيق، ستحصل على:
- **API ID**: رقم (مثل: 1234567)
- **API Hash**: نص طويل (مثل: abcdef1234567890abcdef1234567890)

### 5. إضافة البيانات للبوت
افتح ملف `config.py` وضع البيانات:

```python
# إعدادات Telegram API
API_ID = "1234567"  # ضع API ID هنا (بين علامات التنصيص)
API_HASH = "abcdef1234567890abcdef1234567890"  # ضع API Hash هنا
```

## مثال كامل لملف config.py:

```python
# إعدادات البوت
BOT_TOKEN = "8415486238:AAFpoUZq_OXzZh74o8GrHmXXI7LwqPikSxI"

# إعدادات Telegram API (مطلوبة لقراءة المنشورات من القنوات)
API_ID = "1234567"  # ضع API ID الخاص بك هنا
API_HASH = "abcdef1234567890abcdef1234567890"  # ضع API Hash الخاص بك هنا

# المنشورات المحفوظة للمقارنة
SAVED_POSTS = [
    "نص المنشور الأول المحفوظ",
    "نص المنشور الثاني المحفوظ",
    "نص المنشور الثالث المحفوظ",
]

# نسبة التشابه المطلوبة
SIMILARITY_THRESHOLD = 0.8
```

## ملاحظات مهمة:

### 🔒 الأمان:
- **لا تشارك** API ID و API Hash مع أحد
- هذه البيانات تعطي وصولاً لحسابك في تيليجرام
- احتفظ بها سرية مثل كلمة المرور

### 📱 الاستخدام:
- API ID و API Hash مرتبطان برقم هاتفك
- يمكن استخدامهما في عدة تطبيقات
- لا تحتاج لإنشاء تطبيق جديد لكل بوت

### ⚠️ القيود:
- لا تستخدم نفس API credentials في أكثر من جلسة واحدة في نفس الوقت
- احترم حدود معدل الطلبات في تيليجرام
- استخدمها فقط للأغراض المشروعة

## اختبار الإعداد:

بعد إضافة API ID و API Hash، يمكنك اختبار الإعداد:

```bash
python -c "from telegram_reader import test_telegram_reader; import asyncio; asyncio.run(test_telegram_reader())"
```

إذا ظهرت رسالة "✅ تم تهيئة القارئ بنجاح"، فالإعداد صحيح.

## استكشاف الأخطاء:

### خطأ "Invalid API ID or Hash":
- تأكد من صحة API ID و API Hash
- تأكد من وضعهما بين علامات التنصيص في config.py

### خطأ "Phone number flood":
- انتظر بعض الوقت قبل المحاولة مرة أخرى
- تأكد من عدم استخدام نفس الرقم في عدة تطبيقات

### خطأ "Session file":
- احذف ملف `bot_session.session` إذا وُجد
- أعد تشغيل البوت

## الدعم:

إذا واجهت مشاكل:
1. تأكد من صحة البيانات في config.py
2. تأكد من اتصال الإنترنت
3. جرب إنشاء تطبيق جديد في my.telegram.org
