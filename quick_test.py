#!/usr/bin/env python3
"""
اختبار سريع للبوت
"""

print("🔍 اختبار سريع للبوت...")
print("=" * 30)

# اختبار 1: المكتبات الأساسية
try:
    import sys
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
except:
    print("❌ مشكلة في Python")
    exit(1)

# اختبار 2: ملف الإعدادات
try:
    from config import BOT_TOKEN
    if BOT_TOKEN and BOT_TOKEN != "YOUR_BOT_TOKEN_HERE":
        print("✅ رمز البوت معد")
    else:
        print("❌ رمز البوت غير معد")
        exit(1)
except Exception as e:
    print(f"❌ مشكلة في config.py: {e}")
    exit(1)

# اختبار 3: مكتبة telegram
try:
    import telegram
    print("✅ مكتبة telegram متاحة")
except ImportError:
    print("❌ مكتبة telegram غير متاحة")
    print("💡 قم بتثبيتها: python -m pip install python-telegram-bot")
    exit(1)

# اختبار 4: إنشاء البوت
try:
    from telegram.ext import Application
    app = Application.builder().token(BOT_TOKEN).build()
    print("✅ تم إنشاء البوت بنجاح")
except Exception as e:
    print(f"❌ فشل في إنشاء البوت: {e}")
    exit(1)

print("\n🎉 جميع الاختبارات نجحت!")
print("\n📋 الخطوات التالية:")
print("1. تشغيل البوت المبسط: python minimal_bot.py")
print("2. تشغيل البوت الكامل: python bot.py")
print("3. تشخيص شامل: python debug_bot.py")

print("\n💡 نصائح:")
print("- تأكد من اتصال الإنترنت")
print("- ابحث عن البوت في تيليجرام وأرسل /start")
print("- إذا لم يرد البوت، تحقق من رمز البوت")

input("\nاضغط Enter للمتابعة...")
