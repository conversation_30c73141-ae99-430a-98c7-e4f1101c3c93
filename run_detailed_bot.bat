@echo off
chcp 65001 > nul
title بوت تحليل المنشورات - التقرير المفصل
echo 🤖 بوت تحليل المنشورات - التقرير المفصل
echo ===============================================
echo.

echo 🔍 فحص Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متاح
    pause
    exit /b 1
)
echo ✅ Python متاح

echo.
echo 🧪 فحص مكتبة telegram...
python -c "import telegram; print('✅ مكتبة telegram متاحة')" 2>nul
if errorlevel 1 (
    echo ❌ مكتبة telegram غير متاحة
    echo 💡 تثبيت المكتبة...
    python -m pip install python-telegram-bot
    if errorlevel 1 (
        echo ❌ فشل في التثبيت
        pause
        exit /b 1
    )
)

echo.
echo 🚀 تشغيل البوت مع التقرير المفصل...
echo.
echo 💡 المميزات الجديدة:
echo   - عرض عدد المنشورات المحفوظة
echo   - تقرير يومي مفصل مع التواريخ
echo   - ترقيم المنشورات (الأول = رقم 1)
echo   - حساب الأيام مع/بدون نشر
echo   - عرض "لم يُنشر" للأيام الفارغة
echo.
echo 💡 ابحث عن البوت في تيليجرام وأرسل /start
echo 💡 اضغط Ctrl+C لإيقاف البوت
echo.

python tested_bot.py

echo.
echo 👋 تم إيقاف البوت
pause
