@echo off
chcp 65001 > nul
title بوت تحليل المنشورات - النسخة المضمونة
echo 🤖 بوت تحليل المنشورات - النسخة المضمونة
echo ===============================================
echo.

echo 🔍 فحص النظام...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متاح
    echo 💡 قم بتثبيت Python من https://python.org
    pause
    exit /b 1
)

echo ✅ Python متاح
echo.

echo 🧪 فحص الإعدادات...
python -c "from config import BOT_TOKEN, API_ID, API_HASH; print('✅ جميع الإعدادات معدة بشكل صحيح')" 2>nul
if errorlevel 1 (
    echo ❌ مشكلة في ملف config.py
    echo 💡 تأكد من إعداد BOT_TOKEN و API_ID و API_HASH
    pause
    exit /b 1
)

echo.
echo 🚀 تشغيل البوت المضمون...
echo.
echo 💡 المميزات:
echo   - حساب عدد المنشورات تلقائياً
echo   - قائمة أوامر في الشات (/start)
echo   - معالجة محسنة للرسائل
echo   - تقارير مفصلة
echo.
echo 💡 ابحث عن البوت في تيليجرام وأرسل /start
echo 💡 اضغط Ctrl+C لإيقاف البوت
echo.
echo ⏳ بدء التشغيل...
echo.

python working_telegram_bot.py

echo.
echo 👋 تم إيقاف البوت
pause
