#!/usr/bin/env python3
"""
بوت تحليل المنشورات - نسخة مضمونة العمل
"""

import logging
import re
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, BotCommand
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes

from config import BOT_TOKEN, SAVED_POSTS, SIMILARITY_THRESHOLD, API_ID, API_HASH
from utils import check_post_similarity
from telegram_reader import analyze_telegram_posts, TelegramReader

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# تخزين بيانات المستخدمين
user_sessions = {}

class UserSession:
    def __init__(self):
        self.state = "IDLE"
        self.first_url = None
        self.last_url = None
        self.first_channel = None
        self.first_message_id = None
        self.last_channel = None
        self.last_message_id = None
        self.total_posts = 0

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج أمر /start"""
    user_id = update.effective_user.id
    user_sessions[user_id] = UserSession()
    
    keyboard = [
        [InlineKeyboardButton("📊 تقرير عرض النشر اليومي", callback_data='daily_report')],
        [InlineKeyboardButton("📢 تعميم آخر نشر", callback_data='latest_post')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    welcome_message = """🤖 مرحباً بك في بوت تحليل المنشورات!

اختر أحد الخيارات التالية:"""
    
    await update.message.reply_text(welcome_message, reply_markup=reply_markup)

async def button_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج الأزرار"""
    query = update.callback_query
    await query.answer()
    
    user_id = query.from_user.id
    
    if user_id not in user_sessions:
        user_sessions[user_id] = UserSession()
    
    if query.data == 'daily_report':
        user_sessions[user_id].state = "WAITING_FIRST_URL"
        
        await query.edit_message_text(
            "📊 تقرير عرض النشر اليومي\n\n"
            "الرجاء إرسال رابط أول منشور:\n"
            "(مثال: https://t.me/channel/123)\n\n"
            "أو أرسل /cancel للإلغاء"
        )
        
    elif query.data == 'latest_post':
        await query.edit_message_text(
            "📢 تعميم آخر نشر\n\n"
            "هذه الميزة قيد التطوير...\n\n"
            "أرسل /start للعودة للقائمة الرئيسية"
        )
    
    elif query.data == 'start_calculation':
        await start_calculation(update, context)

async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج الرسائل النصية"""
    user_id = update.effective_user.id
    message_text = update.message.text.strip()
    
    if user_id not in user_sessions:
        await update.message.reply_text("أرسل /start للبدء")
        return
    
    session = user_sessions[user_id]
    
    if session.state == "WAITING_FIRST_URL":
        await handle_first_url(update, context, session)
    elif session.state == "WAITING_LAST_URL":
        await handle_last_url(update, context, session)
    else:
        await update.message.reply_text("أرسل /start للبدء")

async def handle_first_url(update: Update, context: ContextTypes.DEFAULT_TYPE, session: UserSession) -> None:
    """معالج رابط أول منشور"""
    url = update.message.text.strip()
    
    # التحقق من صحة الرابط
    if not re.match(r'https?://', url):
        await update.message.reply_text(
            "❌ الرجاء إرسال رابط صحيح يبدأ بـ http:// أو https://\n"
            "مثال: https://t.me/channel/123"
        )
        return
    
    # التحقق من أن الرابط من تيليجرام
    if not any(domain in url.lower() for domain in ['t.me', 'telegram.me', 'telegram.org']):
        await update.message.reply_text(
            "❌ حالياً البوت يدعم فقط روابط قنوات تيليجرام\n"
            "مثال: https://t.me/channel/123"
        )
        return
    
    # استخراج معلومات الرابط
    reader = TelegramReader()
    channel_info = reader.extract_channel_info(url)
    
    if not channel_info:
        await update.message.reply_text(
            "❌ رابط غير صحيح. تأكد من أن الرابط بالشكل:\n"
            "https://t.me/channel_name/message_id"
        )
        return
    
    # حفظ الرابط الأول
    session.first_url = url
    session.first_channel = channel_info['channel']
    session.first_message_id = channel_info['message_id']
    session.state = "WAITING_LAST_URL"
    
    await update.message.reply_text(
        f"✅ تم حفظ رابط أول منشور\n"
        f"📍 القناة: @{channel_info['channel']}\n"
        f"📝 رقم المنشور: {channel_info['message_id']}\n\n"
        f"الآن الرجاء إرسال رابط آخر منشور من نفس القناة:\n"
        f"(مثال: https://t.me/{channel_info['channel']}/456)"
    )

async def handle_last_url(update: Update, context: ContextTypes.DEFAULT_TYPE, session: UserSession) -> None:
    """معالج رابط آخر منشور"""
    url = update.message.text.strip()
    
    # التحقق من صحة الرابط
    if not re.match(r'https?://', url):
        await update.message.reply_text(
            "❌ الرجاء إرسال رابط صحيح يبدأ بـ http:// أو https://\n"
            "مثال: https://t.me/channel/456"
        )
        return
    
    # التحقق من أن الرابط من تيليجرام
    if not any(domain in url.lower() for domain in ['t.me', 'telegram.me', 'telegram.org']):
        await update.message.reply_text(
            "❌ حالياً البوت يدعم فقط روابط قنوات تيليجرام\n"
            "مثال: https://t.me/channel/456"
        )
        return
    
    # استخراج معلومات الرابط
    reader = TelegramReader()
    channel_info = reader.extract_channel_info(url)
    
    if not channel_info:
        await update.message.reply_text(
            "❌ رابط غير صحيح. تأكد من أن الرابط بالشكل:\n"
            "https://t.me/channel_name/message_id"
        )
        return
    
    # التحقق من أن الرابط من نفس القناة
    if channel_info['channel'] != session.first_channel:
        await update.message.reply_text(
            f"❌ يجب أن يكون الرابط من نفس القناة: @{session.first_channel}\n"
            f"الرابط المرسل من قناة: @{channel_info['channel']}\n\n"
            f"الرجاء إرسال رابط من القناة الصحيحة:\n"
            f"https://t.me/{session.first_channel}/message_id"
        )
        return
    
    # حساب عدد المنشورات
    start_id = min(session.first_message_id, channel_info['message_id'])
    end_id = max(session.first_message_id, channel_info['message_id'])
    total_posts = end_id - start_id + 1
    
    # حفظ البيانات
    session.last_url = url
    session.last_channel = channel_info['channel']
    session.last_message_id = channel_info['message_id']
    session.total_posts = total_posts
    session.state = "READY_TO_CALCULATE"
    
    # إنشاء زر ابدأ الحساب
    keyboard = [[InlineKeyboardButton("🚀 ابدأ الحساب", callback_data='start_calculation')]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await update.message.reply_text(
        f"✅ تم حفظ رابط آخر منشور\n\n"
        f"📊 معلومات التحليل:\n"
        f"📍 القناة: @{channel_info['channel']}\n"
        f"📝 من المنشور: {start_id}\n"
        f"📝 إلى المنشور: {end_id}\n"
        f"🔢 إجمالي المنشورات: {total_posts} منشور\n\n"
        f"اضغط على الزر أدناه لبدء عملية التحليل:",
        reply_markup=reply_markup
    )

async def start_calculation(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """بدء عملية الحساب والتحليل"""
    query = update.callback_query
    await query.answer()
    
    user_id = query.from_user.id
    
    if user_id not in user_sessions or user_sessions[user_id].state != "READY_TO_CALCULATE":
        await query.edit_message_text("❌ خطأ: لم يتم العثور على البيانات المطلوبة")
        return
    
    session = user_sessions[user_id]
    
    await query.edit_message_text(
        f"🔄 جاري تحليل {session.total_posts} منشور من قناة @{session.first_channel}...\n"
        f"📝 من المنشور {min(session.first_message_id, session.last_message_id)} "
        f"إلى {max(session.first_message_id, session.last_message_id)}\n\n"
        f"الرجاء الانتظار..."
    )
    
    try:
        # تحليل المنشورات
        analysis = await analyze_telegram_posts(session.first_url, session.last_url)
        
        if not analysis['success']:
            await query.edit_message_text(f"❌ خطأ في تحليل قناة تيليجرام: {analysis.get('error', 'خطأ غير معروف')}")
            return
        
        # إنشاء التقرير
        report = create_detailed_report(session, analysis)
        
        # إرسال التقرير
        if len(report) > 4000:
            parts = [report[i:i+4000] for i in range(0, len(report), 4000)]
            await query.edit_message_text(parts[0])
            for part in parts[1:]:
                await context.bot.send_message(chat_id=query.message.chat_id, text=part)
        else:
            await query.edit_message_text(report)
        
        # إعادة تعيين الجلسة
        user_sessions[user_id] = UserSession()
        
        # زر العودة
        keyboard = [[InlineKeyboardButton("🔙 بدء تحليل جديد", callback_data='daily_report')]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await context.bot.send_message(
            chat_id=query.message.chat_id, 
            text="أرسل /start لبدء تحليل جديد",
            reply_markup=reply_markup
        )
        
    except Exception as e:
        logger.error(f"خطأ في التحليل: {e}")
        await query.edit_message_text(f"❌ حدث خطأ أثناء التحليل:\n{str(e)}\n\nأرسل /start للمحاولة مرة أخرى")

def create_detailed_report(session: UserSession, analysis: dict) -> str:
    """إنشاء تقرير مفصل"""
    messages = analysis['messages']
    
    if not messages:
        return f"""❌ لم يتم العثور على رسائل في النطاق المحدد

📊 معلومات البحث:
• القناة: @{session.first_channel}
• النطاق: من {min(session.first_message_id, session.last_message_id)} إلى {max(session.first_message_id, session.last_message_id)}
• العدد المتوقع: {session.total_posts} منشور

💡 تأكد من أن:
- القناة عامة ومتاحة
- أرقام المنشورات صحيحة
- المنشورات موجودة فعلاً"""
    
    # تحليل المنشورات
    matching_posts = 0
    daily_report = []
    
    # تجميع الرسائل حسب التاريخ
    daily_messages = {}
    for msg in messages:
        date_str = msg['date'].strftime('%Y-%m-%d')
        if date_str not in daily_messages:
            daily_messages[date_str] = []
        daily_messages[date_str].append(msg)
    
    # فحص كل يوم
    for date_str in sorted(daily_messages.keys()):
        day_messages = daily_messages[date_str]
        matching_count = 0
        
        for msg in day_messages:
            if check_post_similarity(msg['content'], SAVED_POSTS, SIMILARITY_THRESHOLD):
                matching_count += 1
        
        if matching_count > 0:
            matching_posts += matching_count
            if matching_count == 1:
                daily_report.append(f"{date_str}: تم نشر منشور مطابق")
            else:
                daily_report.append(f"{date_str}: تم نشر {matching_count} منشورات مطابقة")
        else:
            daily_report.append(f"{date_str}: لم يُنشر محتوى مطابق")
    
    days_without_posts = len([line for line in daily_report if "لم يُنشر" in line])
    
    # حساب النسب المئوية
    matching_percentage = (matching_posts / len(messages)) * 100 if messages else 0
    coverage_percentage = (len(messages) / session.total_posts) * 100 if session.total_posts > 0 else 0
    
    # إنشاء التقرير النهائي
    report = f"""📊 تقرير عرض النشر اليومي

📈 إحصائيات عامة:
• القناة: @{session.first_channel}
• النطاق: من المنشور {min(session.first_message_id, session.last_message_id)} إلى {max(session.first_message_id, session.last_message_id)}
• العدد المتوقع: {session.total_posts} منشور
• الرسائل المجلبة: {len(messages)} رسالة
• نسبة التغطية: {coverage_percentage:.1f}%

🎯 نتائج التحليل:
• المنشورات المطابقة: {matching_posts}
• نسبة التطابق: {matching_percentage:.1f}%
• الأيام بدون نشر مطابق: {days_without_posts}
• إجمالي الأيام: {len(daily_messages)}

📅 التقرير اليومي:
{chr(10).join(daily_report)}

✅ تم إنشاء التقرير بنجاح"""
    
    return report.strip()

async def cancel(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """إلغاء المحادثة"""
    user_id = update.effective_user.id
    if user_id in user_sessions:
        user_sessions[user_id] = UserSession()
    
    await update.message.reply_text(
        "تم إلغاء العملية.\n\nأرسل /start للبدء من جديد."
    )

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج أمر /help"""
    help_text = """🤖 بوت تحليل المنشورات

الأوامر المتاحة:
/start - بدء البوت وعرض القائمة الرئيسية
/help - عرض هذه المساعدة
/cancel - إلغاء العملية الحالية

كيفية الاستخدام:
1. أرسل /start
2. اختر "تقرير عرض النشر اليومي"
3. أرسل رابط أول منشور من قناة تيليجرام
4. أرسل رابط آخر منشور من نفس القناة
5. سيحسب البوت العدد تلقائياً
6. اضغط "ابدأ الحساب"

مثال:
• الأول: https://t.me/telegram/1
• الأخير: https://t.me/telegram/10
• النتيجة: تحليل 10 منشورات"""
    
    await update.message.reply_text(help_text)

async def set_bot_commands(application):
    """إعداد قائمة الأوامر للبوت"""
    commands = [
        BotCommand("start", "بدء البوت وعرض القائمة الرئيسية"),
        BotCommand("help", "عرض المساعدة والتعليمات"),
        BotCommand("cancel", "إلغاء العملية الحالية"),
    ]
    
    try:
        await application.bot.set_my_commands(commands)
        print("✅ تم إعداد قائمة الأوامر")
    except Exception as e:
        print(f"⚠️ فشل في إعداد قائمة الأوامر: {e}")

def main() -> None:
    """تشغيل البوت"""
    print("🤖 بدء تشغيل بوت تحليل المنشورات...")
    print(f"🔑 رمز البوت: {BOT_TOKEN[:10]}...")
    print(f"🔧 API ID: {API_ID}")
    
    try:
        # إنشاء التطبيق
        application = Application.builder().token(BOT_TOKEN).build()
        
        # إضافة المعالجات
        application.add_handler(CommandHandler('start', start))
        application.add_handler(CommandHandler('help', help_command))
        application.add_handler(CommandHandler('cancel', cancel))
        application.add_handler(CallbackQueryHandler(button_handler))
        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))
        
        # إعداد قائمة الأوامر
        async def post_init(application):
            await set_bot_commands(application)
        
        application.post_init = post_init
        
        print("✅ تم إعداد جميع المعالجات")
        print("🚀 البوت يعمل الآن...")
        print("💡 ابحث عن البوت في تيليجرام وأرسل /start")
        print("💡 اضغط Ctrl+C لإيقاف البوت")
        print("-" * 50)
        
        # تشغيل البوت
        application.run_polling(
            allowed_updates=Update.ALL_TYPES,
            drop_pending_updates=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البوت: {e}")
        logger.error(f"خطأ في تشغيل البوت: {e}")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
