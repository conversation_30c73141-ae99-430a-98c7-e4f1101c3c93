#!/usr/bin/env python3
"""
اختبار بسيط للتأكد من عمل Python والمكتبات الأساسية
"""

import sys
import os

def test_python():
    """اختبار Python"""
    print(f"✅ Python {sys.version}")
    print(f"✅ نظام التشغيل: {os.name}")
    return True

def test_imports():
    """اختبار استيراد المكتبات"""
    try:
        import difflib
        print("✅ difflib متاح")
    except ImportError as e:
        print(f"❌ difflib غير متاح: {e}")
        return False
    
    try:
        import re
        print("✅ re متاح")
    except ImportError as e:
        print(f"❌ re غير متاح: {e}")
        return False
    
    try:
        from datetime import datetime, timedelta
        print("✅ datetime متاح")
    except ImportError as e:
        print(f"❌ datetime غير متاح: {e}")
        return False
    
    # اختبار المكتبات الخارجية
    external_libs = [
        ('telegram', 'python-telegram-bot'),
        ('requests', 'requests'),
        ('bs4', 'beautifulsoup4'),
        ('dateutil', 'python-dateutil')
    ]
    
    missing_libs = []
    for lib_name, package_name in external_libs:
        try:
            __import__(lib_name)
            print(f"✅ {package_name} متاح")
        except ImportError:
            print(f"❌ {package_name} غير متاح")
            missing_libs.append(package_name)
    
    if missing_libs:
        print(f"\n📦 المكتبات المطلوب تثبيتها:")
        for lib in missing_libs:
            print(f"   pip install {lib}")
        return False
    
    return True

def test_config():
    """اختبار ملف الإعدادات"""
    try:
        from config import BOT_TOKEN, SAVED_POSTS, SIMILARITY_THRESHOLD
        
        if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
            print("❌ يجب إعداد BOT_TOKEN في config.py")
            return False
        else:
            print("✅ BOT_TOKEN معد بشكل صحيح")
        
        print(f"✅ عدد المنشورات المحفوظة: {len(SAVED_POSTS)}")
        print(f"✅ نسبة التشابه: {SIMILARITY_THRESHOLD}")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد config.py: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في config.py: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار بسيط للبوت")
    print("=" * 40)
    
    # اختبار Python
    if not test_python():
        return False
    
    print("\n📚 اختبار المكتبات:")
    if not test_imports():
        print("\n⚠️ بعض المكتبات غير متاحة. قم بتثبيتها أولاً.")
        return False
    
    print("\n⚙️ اختبار الإعدادات:")
    if not test_config():
        return False
    
    print("\n🎉 جميع الاختبارات نجحت!")
    print("يمكنك الآن تشغيل البوت باستخدام: python bot.py")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ فشل في بعض الاختبارات")
            input("اضغط Enter للخروج...")
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
