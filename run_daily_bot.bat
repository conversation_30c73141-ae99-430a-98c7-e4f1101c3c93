@echo off
chcp 65001 > nul
title بوت تحليل المنشورات - منشور واحد لكل يوم
echo 🤖 بوت تحليل المنشورات - منشور واحد لكل يوم
echo ===============================================
echo.

echo 🔍 فحص Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متاح
    echo 💡 تأكد من تثبيت Python أولاً
    pause
    exit /b 1
)
echo ✅ Python متاح

echo.
echo 🧪 فحص مكتبة telegram...
python -c "import telegram; print('✅ مكتبة telegram متاحة')" 2>nul
if errorlevel 1 (
    echo ❌ مكتبة telegram غير متاحة
    echo 💡 تثبيت المكتبة...
    python -m pip install python-telegram-bot
    if errorlevel 1 (
        echo ❌ فشل في التثبيت
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبة
)

echo.
echo 🚀 تشغيل البوت - منشور واحد لكل يوم...
echo.
echo ✅ النظام الجديد:
echo   📅 منشور واحد لكل يوم
echo   🔢 ترقيم تسلسلي: الرابط الأول = منشور رقم 1
echo   📊 عرض عدد المنشورات المطابقة في نفس اليوم
echo   📅 تواريخ صحيحة ومتسلسلة
echo   ❌ عرض "لم يتم النشر" للأيام الفارغة
echo.
echo 💡 أمثلة على النتائج:
echo   2024-12-15: منشور رقم 1
echo   2024-12-16: منشور رقم 3 (2 منشورات مطابقة)
echo   2024-12-17: لم يتم النشر
echo   2024-12-18: منشور رقم 7
echo.
echo 🎯 المنطق:
echo   - كل يوم يعرض منشور واحد فقط
echo   - إذا كان هناك عدة منشورات مطابقة في نفس اليوم
echo   - يعرض أول منشور + عدد المنشورات المطابقة
echo   - الترقيم تسلسلي حسب ترتيب الروابط
echo.
echo 💡 كيفية الاستخدام:
echo   1. ابحث عن البوت في تيليجرام
echo   2. اضغط /start من قائمة الأوامر
echo   3. اختر "📊 تقرير عرض النشر اليومي"
echo   4. أرسل رابط أول منشور
echo   5. أرسل رابط آخر منشور
echo   6. اضغط "🚀 ابدأ الحساب"
echo.
echo 🧪 للاختبار:
echo   - الأول: https://t.me/telegram/1
echo   - الأخير: https://t.me/telegram/20
echo   - النتيجة: تقرير يومي مع منشور واحد لكل يوم
echo.
echo 💡 اضغط Ctrl+C لإيقاف البوت
echo.

python corrected_bot.py

echo.
echo 👋 تم إيقاف البوت
pause
