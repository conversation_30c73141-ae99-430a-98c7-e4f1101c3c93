#!/usr/bin/env python3
"""
ملف اختبار البوت
"""

import asyncio
from utils import calculate_similarity, clean_text, check_post_similarity
from config import SAVED_POSTS, SIMILARITY_THRESHOLD

def test_similarity():
    """اختبار دالة حساب التشابه"""
    print("🧪 اختبار دالة حساب التشابه...")
    
    # نصوص للاختبار
    text1 = "هذا نص تجريبي للاختبار"
    text2 = "هذا نص تجريبي للاختبار"
    text3 = "نص مختلف تماماً"
    
    similarity1 = calculate_similarity(text1, text2)
    similarity2 = calculate_similarity(text1, text3)
    
    print(f"التشابه بين النصين المتطابقين: {similarity1:.2%}")
    print(f"التشابه بين النصين المختلفين: {similarity2:.2%}")
    
    assert similarity1 == 1.0, "النصان المتطابقان يجب أن يكون تشابههما 100%"
    assert similarity2 < 0.5, "النصان المختلفان يجب أن يكون تشابههما أقل من 50%"
    
    print("✅ اختبار التشابه نجح!")

def test_text_cleaning():
    """اختبار دالة تنظيف النص"""
    print("\n🧹 اختبار دالة تنظيف النص...")
    
    dirty_text = "هذا نص   مع مسافات زائدة!!! ورابط https://example.com وأرقام 123"
    clean = clean_text(dirty_text)
    
    print(f"النص الأصلي: {dirty_text}")
    print(f"النص المنظف: {clean}")
    
    assert "https://example.com" not in clean, "يجب إزالة الروابط"
    assert "   " not in clean, "يجب إزالة المسافات الزائدة"
    
    print("✅ اختبار تنظيف النص نجح!")

def test_post_similarity():
    """اختبار دالة فحص تشابه المنشورات"""
    print("\n📝 اختبار دالة فحص تشابه المنشورات...")
    
    # إضافة منشور تجريبي للمقارنة
    test_saved_posts = [
        "هذا منشور تجريبي للاختبار",
        "منشور آخر للمقارنة"
    ]
    
    # منشور مشابه
    similar_post = "هذا منشور تجريبي للاختبار مع إضافة بسيطة"
    # منشور مختلف
    different_post = "منشور مختلف تماماً عن المنشورات المحفوظة"
    
    is_similar1 = check_post_similarity(similar_post, test_saved_posts, 0.7)
    is_similar2 = check_post_similarity(different_post, test_saved_posts, 0.7)
    
    print(f"المنشور المشابه: {is_similar1}")
    print(f"المنشور المختلف: {is_similar2}")
    
    assert is_similar1 == True, "يجب أن يكون المنشور المشابه مطابقاً"
    assert is_similar2 == False, "يجب أن يكون المنشور المختلف غير مطابق"
    
    print("✅ اختبار فحص تشابه المنشورات نجح!")

def test_config():
    """اختبار الإعدادات"""
    print("\n⚙️ اختبار الإعدادات...")
    
    from config import BOT_TOKEN, SAVED_POSTS, SIMILARITY_THRESHOLD
    
    print(f"رمز البوت: {'✅ موجود' if BOT_TOKEN != 'YOUR_BOT_TOKEN_HERE' else '❌ يحتاج إعداد'}")
    print(f"المنشورات المحفوظة: {len(SAVED_POSTS)} منشور")
    print(f"نسبة التشابه: {SIMILARITY_THRESHOLD:.0%}")
    
    if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
        print("⚠️ تحذير: يجب إعداد رمز البوت في config.py")

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار البوت...\n")
    
    try:
        test_similarity()
        test_text_cleaning()
        test_post_similarity()
        test_config()
        
        print("\n🎉 جميع الاختبارات نجحت!")
        print("\n📋 خطوات ما بعد الاختبار:")
        print("1. أعد إعداد رمز البوت في config.py")
        print("2. أضف المنشورات المحفوظة في config.py")
        print("3. شغل البوت باستخدام: python bot.py")
        
    except Exception as e:
        print(f"\n❌ فشل في الاختبار: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
