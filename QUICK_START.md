# 🚀 دليل البدء السريع

## ✨ المميزات الجديدة
تم إضافة دعم قراءة المنشورات من قنوات تيليجرام العامة! الآن البوت يمكنه:
- ✅ قراءة المنشورات من أي قناة تيليجرام عامة
- ✅ تحليل المنشورات بين تاريخين محددين
- ✅ العمل مع روابط t.me مباشرة

## ✅ الخطوات:

### 1. تثبيت المتطلبات
اختر إحدى الطرق التالية:

#### الطريقة الأولى (مستحسنة):
```bash
install.bat
```

#### الطريقة الثانية:
```bash
python -m pip install -r requirements.txt
```

#### الطريقة الثالثة (يدوياً):
```bash
python -m pip install python-telegram-bot==20.7
python -m pip install requests==2.31.0
python -m pip install beautifulsoup4==4.12.2
python -m pip install lxml==4.9.3
python -m pip install python-dateutil==2.8.2
```

### 2. اختبار الإعداد
```bash
python simple_test.py
```

### 3. إعداد API credentials (جديد ومهم!)
لقراءة المنشورات من قنوات تيليجرام، تحتاج لإعداد API ID و API Hash:

1. **احصل على API credentials:**
   - اذهب إلى https://my.telegram.org
   - سجل دخول برقم هاتفك
   - أنشئ تطبيق جديد
   - احصل على API ID و API Hash

2. **أضف البيانات لملف config.py:**
   ```python
   API_ID = "1234567"  # ضع API ID هنا
   API_HASH = "your_api_hash_here"  # ضع API Hash هنا
   ```

📖 **للتفاصيل الكاملة**: راجع ملف `HOW_TO_GET_API_CREDENTIALS.md`

### 4. اختبار الإعداد
```bash
python test_api.py
```

### 5. إعداد البوت (إذا لم يكن معداً)
✅ **تم بالفعل**: رمز البوت معد في `config.py`

إذا كنت تريد تغيير الإعدادات:
- افتح `config.py`
- عدل `SAVED_POSTS` لإضافة منشوراتك المحفوظة
- عدل `SIMILARITY_THRESHOLD` لتغيير نسبة التشابه

### 6. تشغيل البوت
```bash
python bot.py
```
أو
```bash
run.bat
```

## 🔧 إذا واجهت مشاكل:

### مشكلة: "pip غير معروف"
**الحل**: استخدم `python -m pip` بدلاً من `pip`

### مشكلة: "Python غير معروف"
**الحل**: 
1. تأكد من تثبيت Python من https://python.org
2. أعد تشغيل Command Prompt
3. تأكد من إضافة Python إلى PATH

### مشكلة: خطأ في تثبيت المكتبات
**الحل**:
1. جرب تشغيل Command Prompt كمدير (Run as Administrator)
2. جرب تحديث pip: `python -m pip install --upgrade pip`
3. جرب تثبيت المكتبات واحدة تلو الأخرى

### مشكلة: خطأ في البوت
**الحل**:
1. تأكد من صحة رمز البوت في `config.py`
2. تأكد من اتصال الإنترنت
3. شغل `python simple_test.py` للتحقق من الإعداد

## 📱 استخدام البوت:

1. ابحث عن البوت في تيليجرام
2. أرسل `/start`
3. اختر "📊 تقرير عرض النشر اليومي"
4. أرسل رابط أول منشور
5. أرسل رابط آخر منشور
6. اضغط "🚀 ابدأ الحساب"

## 📞 المساعدة:

إذا استمرت المشاكل:
1. تأكد من تشغيل `python simple_test.py` بنجاح
2. تحقق من رسائل الخطأ في التيرمينال
3. تأكد من صحة رمز البوت

---

**ملاحظة**: تم إصلاح مشكلة `difflib` - هذه مكتبة مدمجة في Python ولا تحتاج تثبيت منفصل.
