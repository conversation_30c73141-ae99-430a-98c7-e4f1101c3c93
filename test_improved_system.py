#!/usr/bin/env python3
"""
اختبار النظام المحسن للتواريخ والتطابق الدقيق
"""

from datetime import datetime, timedelta
import random
from config import SAVED_POSTS

def is_exact_match(content: str, saved_posts: list) -> bool:
    """فحص التطابق الدقيق مع المنشورات المحفوظة"""
    content_clean = content.strip().lower()
    
    for saved_post in saved_posts:
        saved_clean = saved_post.strip().lower()
        
        # التطابق الدقيق
        if content_clean == saved_clean:
            return True
        
        # التطابق الجزئي (يحتوي على النص المحفوظ كاملاً)
        if saved_clean in content_clean or content_clean in saved_clean:
            return True
    
    return False

def generate_realistic_date(post_number: int, total_posts: int) -> datetime:
    """إنشاء تاريخ واقعي للمنشور المطابق"""
    # تاريخ البداية: قبل شهرين من الآن
    end_date = datetime.now() - timedelta(days=1)  # أمس
    start_date = end_date - timedelta(days=60)  # قبل شهرين
    
    # توزيع المنشورات المطابقة على المدة الزمنية
    if total_posts > 1:
        days_between = (end_date - start_date).days
        day_interval = days_between / (total_posts - 1)
        days_offset = int(day_interval * (post_number - 1))
    else:
        days_offset = 0
    
    # حساب التاريخ
    post_date = start_date + timedelta(days=days_offset)
    
    # إضافة وقت واقعي (بين 9 صباحاً و 9 مساءً)
    hour = random.randint(9, 21)
    minute = random.randint(0, 59)
    
    post_date = post_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
    
    # التأكد من أن التاريخ في الماضي
    if post_date >= datetime.now():
        post_date = datetime.now() - timedelta(days=post_number)
    
    return post_date

def test_improved_system():
    """اختبار النظام المحسن"""
    print("🧪 اختبار النظام المحسن")
    print("=" * 50)
    
    # محاكاة منشورات مختلطة (بعضها مطابق وبعضها غير مطابق)
    test_posts = [
        "تداول مع إنزو دون الحاجة لإيداع أولي, حيث يمكنك الآن فتح حساباً حقيقياً والحصول على بونص ترحيبي بقيمة 30$ لتبدأ رحلتك الأستثمارية على الفور. فضلا على امكانية استخدامك لهذا البونص في صفقاتك, يمكنك سحب أرباحك بكل سهولة تداول مع أفضل وسيط STP موثوق ومرخص من أبرز الهيئات الرقابية في الشرق الأوسط، لضمان تجربة تداول آمنة وشفافة",  # مطابق
        "منشور عشوائي غير مطابق",  # غير مطابق
        "إعلان مهم للمتابعين",  # مطابق
        "نص آخر غير مطابق تماماً",  # غير مطابق
        "تحديث جديد في الخدمات",  # مطابق
        "محتوى عادي لا يطابق",  # غير مطابق
        "خبر عاجل ومهم",  # مطابق
        "منشور ترويجي للخدمات",  # مطابق
    ]
    
    print(f"📊 إجمالي المنشورات للاختبار: {len(test_posts)}")
    print(f"📋 المنشورات المحفوظة: {len(SAVED_POSTS)}")
    print()
    
    # فحص التطابق
    matching_posts = []
    post_counter = 1
    
    print("🔍 فحص التطابق:")
    for i, content in enumerate(test_posts, 1):
        is_match = is_exact_match(content, SAVED_POSTS)
        status = "✅ مطابق" if is_match else "❌ غير مطابق"
        
        print(f"منشور {i}: {status}")
        print(f"   النص: {content[:50]}...")
        
        if is_match:
            matching_posts.append({
                'number': post_counter,
                'content': content,
                'original_id': i
            })
            post_counter += 1
        print()
    
    print(f"📈 النتائج:")
    print(f"• إجمالي المنشورات: {len(test_posts)}")
    print(f"• المنشورات المطابقة: {len(matching_posts)}")
    print(f"• المنشورات غير المطابقة: {len(test_posts) - len(matching_posts)}")
    print()
    
    # اختبار التواريخ الواقعية
    if matching_posts:
        print("📅 اختبار التواريخ الواقعية:")
        total_matching = len(matching_posts)
        
        for i, post in enumerate(matching_posts):
            post['date'] = generate_realistic_date(i + 1, total_matching)
            
        # عرض التواريخ
        for post in matching_posts:
            print(f"المنشور المطابق رقم {post['number']}: {post['date'].strftime('%Y-%m-%d %H:%M')}")
        
        print()
        
        # فحص التواريخ
        print("🔍 فحص التواريخ:")
        current_time = datetime.now()
        
        # فحص أن جميع التواريخ في الماضي
        all_past = all(post['date'] < current_time for post in matching_posts)
        print(f"✅ جميع التواريخ في الماضي: {'نعم' if all_past else 'لا'}")
        
        # فحص التسلسل
        dates = [post['date'] for post in matching_posts]
        is_sequential = all(dates[i] <= dates[i+1] for i in range(len(dates)-1))
        print(f"✅ التواريخ متسلسلة: {'نعم' if is_sequential else 'لا'}")
        
        # فحص النطاق الزمني
        if dates:
            oldest_date = min(dates)
            newest_date = max(dates)
            days_range = (newest_date - oldest_date).days
            
            print(f"📊 نطاق التواريخ:")
            print(f"   من: {oldest_date.strftime('%Y-%m-%d')}")
            print(f"   إلى: {newest_date.strftime('%Y-%m-%d')}")
            print(f"   المدة: {days_range} يوم")
    
    print()
    print("✅ انتهى الاختبار")

def test_exact_matching():
    """اختبار التطابق الدقيق"""
    print("\n🎯 اختبار التطابق الدقيق")
    print("=" * 30)
    
    test_cases = [
        ("إعلان مهم للمتابعين", True),  # تطابق دقيق
        ("إعلان مهم للمتابعين والأصدقاء", True),  # يحتوي على النص المحفوظ
        ("إعلان مهم", False),  # جزء من النص فقط
        ("إعلان غير مهم", False),  # مختلف تماماً
        ("تحديث جديد في الخدمات", True),  # تطابق دقيق
        ("تحديث قديم في الخدمات", False),  # مختلف
    ]
    
    for text, expected in test_cases:
        result = is_exact_match(text, SAVED_POSTS)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{text}' -> {'مطابق' if result else 'غير مطابق'}")

if __name__ == "__main__":
    test_improved_system()
    test_exact_matching()
    input("\nاضغط Enter للخروج...")
