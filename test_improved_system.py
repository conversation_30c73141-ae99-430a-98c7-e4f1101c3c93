#!/usr/bin/env python3
"""
اختبار النظام المحسن للتواريخ والتطابق الدقيق
"""

from datetime import datetime, timedelta
import random
from config import SAVED_POSTS

def calculate_similarity(text1: str, text2: str) -> float:
    """حساب التشابه بين نصين باستخدام difflib للدقة العالية"""
    import difflib

    # تنظيف النصوص
    text1 = text1.lower().strip()
    text2 = text2.lower().strip()

    # حساب التشابه باستخدام SequenceMatcher للدقة العالية
    similarity = difflib.SequenceMatcher(None, text1, text2).ratio()
    return similarity

def check_post_similarity(content: str, saved_posts: list, threshold: float = 0.8) -> bool:
    """فحص ما إذا كان المنشور يتشابه مع المنشورات المحفوظة بنسبة أعلى من 80%"""
    for saved_post in saved_posts:
        similarity = calculate_similarity(content, saved_post)
        if similarity > threshold:  # أعلى من 80%
            return True
    return False

def get_highest_similarity(content: str, saved_posts: list) -> float:
    """الحصول على أعلى نسبة تشابه مع المنشورات المحفوظة"""
    max_similarity = 0.0
    for saved_post in saved_posts:
        similarity = calculate_similarity(content, saved_post)
        max_similarity = max(max_similarity, similarity)
    return max_similarity

def generate_realistic_date(post_number: int, total_posts: int) -> datetime:
    """إنشاء تاريخ واقعي للمنشور المطابق"""
    # تاريخ البداية: قبل شهرين من الآن
    end_date = datetime.now() - timedelta(days=1)  # أمس
    start_date = end_date - timedelta(days=60)  # قبل شهرين
    
    # توزيع المنشورات المطابقة على المدة الزمنية
    if total_posts > 1:
        days_between = (end_date - start_date).days
        day_interval = days_between / (total_posts - 1)
        days_offset = int(day_interval * (post_number - 1))
    else:
        days_offset = 0
    
    # حساب التاريخ
    post_date = start_date + timedelta(days=days_offset)
    
    # إضافة وقت واقعي (بين 9 صباحاً و 9 مساءً)
    hour = random.randint(9, 21)
    minute = random.randint(0, 59)
    
    post_date = post_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
    
    # التأكد من أن التاريخ في الماضي
    if post_date >= datetime.now():
        post_date = datetime.now() - timedelta(days=post_number)
    
    return post_date

def test_improved_system():
    """اختبار النظام المحسن"""
    print("🧪 اختبار النظام المحسن")
    print("=" * 50)
    
    # محاكاة منشورات مختلطة (بعضها مطابق وبعضها غير مطابق)
    test_posts = [
        "تداول مع إنزو دون الحاجة لإيداع أولي, حيث يمكنك الآن فتح حساباً حقيقياً والحصول على بونص ترحيبي بقيمة 30$ لتبدأ رحلتك الأستثمارية على الفور. فضلا على امكانية استخدامك لهذا البونص في صفقاتك, يمكنك سحب أرباحك بكل سهولة تداول مع أفضل وسيط STP موثوق ومرخص من أبرز الهيئات الرقابية في الشرق الأوسط، لضمان تجربة تداول آمنة وشفافة",  # مطابق
        "منشور عشوائي غير مطابق",  # غير مطابق
        "إعلان مهم للمتابعين",  # مطابق
        "نص آخر غير مطابق تماماً",  # غير مطابق
        "تحديث جديد في الخدمات",  # مطابق
        "محتوى عادي لا يطابق",  # غير مطابق
        "خبر عاجل ومهم",  # مطابق
        "منشور ترويجي للخدمات",  # مطابق
    ]
    
    print(f"📊 إجمالي المنشورات للاختبار: {len(test_posts)}")
    print(f"📋 المنشورات المحفوظة: {len(SAVED_POSTS)}")
    print()
    
    # فحص التطابق بنسبة أعلى من 80%
    matching_posts = []
    post_counter = 1

    print("🔍 فحص التطابق (نسبة أعلى من 80%):")
    for i, content in enumerate(test_posts, 1):
        similarity = get_highest_similarity(content, SAVED_POSTS)
        is_match = check_post_similarity(content, SAVED_POSTS, threshold=0.8)

        similarity_percent = int(similarity * 100)
        status = f"✅ مطابق ({similarity_percent}%)" if is_match else f"❌ غير مطابق ({similarity_percent}%)"

        print(f"منشور {i}: {status}")
        print(f"   النص: {content[:50]}...")

        if is_match:
            matching_posts.append({
                'number': post_counter,
                'content': content,
                'original_id': i,
                'similarity': similarity
            })
            post_counter += 1
        print()
    
    print(f"📈 النتائج:")
    print(f"• إجمالي المنشورات: {len(test_posts)}")
    print(f"• المنشورات المطابقة: {len(matching_posts)}")
    print(f"• المنشورات غير المطابقة: {len(test_posts) - len(matching_posts)}")
    print()
    
    # اختبار التواريخ الواقعية
    if matching_posts:
        print("📅 اختبار التواريخ الواقعية:")
        total_matching = len(matching_posts)
        
        for i, post in enumerate(matching_posts):
            post['date'] = generate_realistic_date(i + 1, total_matching)
            
        # عرض التواريخ
        for post in matching_posts:
            print(f"المنشور المطابق رقم {post['number']}: {post['date'].strftime('%Y-%m-%d %H:%M')}")
        
        print()
        
        # فحص التواريخ
        print("🔍 فحص التواريخ:")
        current_time = datetime.now()
        
        # فحص أن جميع التواريخ في الماضي
        all_past = all(post['date'] < current_time for post in matching_posts)
        print(f"✅ جميع التواريخ في الماضي: {'نعم' if all_past else 'لا'}")
        
        # فحص التسلسل
        dates = [post['date'] for post in matching_posts]
        is_sequential = all(dates[i] <= dates[i+1] for i in range(len(dates)-1))
        print(f"✅ التواريخ متسلسلة: {'نعم' if is_sequential else 'لا'}")
        
        # فحص النطاق الزمني
        if dates:
            oldest_date = min(dates)
            newest_date = max(dates)
            days_range = (newest_date - oldest_date).days
            
            print(f"📊 نطاق التواريخ:")
            print(f"   من: {oldest_date.strftime('%Y-%m-%d')}")
            print(f"   إلى: {newest_date.strftime('%Y-%m-%d')}")
            print(f"   المدة: {days_range} يوم")
    
    print()
    print("✅ انتهى الاختبار")

def test_similarity_threshold():
    """اختبار نسبة التشابه أعلى من 80%"""
    print("\n🎯 اختبار نسبة التشابه أعلى من 80%")
    print("=" * 40)

    test_cases = [
        ("تداول مع إنزو دون الحاجة لإيداع أولي, حيث يمكنك الآن فتح حساباً حقيقياً والحصول على بونص ترحيبي بقيمة 30$ لتبدأ رحلتك الأستثمارية على الفور. فضلا على امكانية استخدامك لهذا البونص في صفقاتك, يمكنك سحب أرباحك بكل سهولة تداول مع أفضل وسيط STP موثوق ومرخص من أبرز الهيئات الرقابية في الشرق الأوسط، لضمان تجربة تداول آمنة وشفافة", True),  # تطابق 100%
        ("إعلان مهم للمتابعين", True),  # تطابق دقيق
        ("إعلان مهم للمتابعين والأصدقاء", False),  # تشابه أقل من 80%
        ("إعلان مهم", False),  # تشابه منخفض
        ("إعلان غير مهم", False),  # مختلف تماماً
        ("تحديث جديد في الخدمات", True),  # تطابق دقيق
        ("تحديث قديم في الخدمات", False),  # تشابه منخفض
    ]

    for text, expected_above_80 in test_cases:
        similarity = get_highest_similarity(text, SAVED_POSTS)
        result = check_post_similarity(text, SAVED_POSTS, threshold=0.8)
        similarity_percent = int(similarity * 100)

        status = "✅" if result == expected_above_80 else "❌"
        match_status = "مطابق" if result else "غير مطابق"

        print(f"{status} '{text[:30]}...' -> {match_status} ({similarity_percent}%)")

if __name__ == "__main__":
    test_improved_system()
    test_similarity_threshold()
    input("\nاضغط Enter للخروج...")
