@echo off
chcp 65001 > nul
title بوت تحليل المنشورات - النسخة النهائية المختبرة
echo 🤖 بوت تحليل المنشورات - النسخة النهائية المختبرة
echo ===============================================
echo.

echo 🔍 فحص Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متاح
    echo 💡 تأكد من تثبيت Python أولاً
    pause
    exit /b 1
)
echo ✅ Python متاح

echo.
echo 🧪 فحص مكتبة telegram...
python -c "import telegram; print('✅ مكتبة telegram متاحة')" 2>nul
if errorlevel 1 (
    echo ❌ مكتبة telegram غير متاحة
    echo 💡 تثبيت المكتبة...
    python -m pip install python-telegram-bot
    if errorlevel 1 (
        echo ❌ فشل في التثبيت
        echo 💡 جرب: pip install python-telegram-bot
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبة
)

echo.
echo 🚀 تشغيل البوت النهائي المختبر...
echo.
echo ✨ المميزات الجديدة:
echo   📋 عرض عدد المنشورات المحفوظة: 8 منشور
echo   📅 تقرير يومي مفصل مع التواريخ
echo   🔢 ترقيم المنشورات (الأول = رقم 1)
echo   📊 حساب الأيام مع/بدون نشر
echo   ❌ عرض "لم يُنشر" للأيام الفارغة
echo   📈 إحصائيات شاملة ومفصلة
echo.
echo 💡 كيفية الاستخدام:
echo   1. ابحث عن البوت في تيليجرام
echo   2. ستجد /start في قائمة الأوامر أسفل الشات
echo   3. اضغط /start
echo   4. اختر "📊 تقرير عرض النشر اليومي"
echo   5. أرسل رابط أول منشور
echo   6. أرسل رابط آخر منشور
echo   7. اضغط "🚀 ابدأ الحساب"
echo.
echo 🧪 للاختبار استخدم:
echo   - الأول: https://t.me/telegram/1
echo   - الأخير: https://t.me/telegram/10
echo   - النتيجة: تقرير مفصل لـ 10 منشورات
echo.
echo 💡 اضغط Ctrl+C لإيقاف البوت
echo.

python final_working_bot.py

echo.
echo 👋 تم إيقاف البوت
pause
