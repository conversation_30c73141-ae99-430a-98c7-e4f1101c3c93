@echo off
echo ========================================
echo   البوت النهائي - بيانات فعلية
echo ========================================
echo.
echo 🎯 الميزات الجديدة:
echo ✅ قراءة البيانات الفعلية من تيليجرام
echo ✅ نسبة تشابه أعلى من 80%% فقط
echo ✅ تواريخ حقيقية من المنشورات الفعلية
echo ✅ أرقام صحيحة ودقيقة (ليست محاكاة)
echo ✅ عرض نسبة التشابه لكل منشور
echo.
echo 📍 المنشورات المحفوظة موجودة في: config.py
echo 📊 نسبة التشابه المطلوبة: أعلى من 80%%
echo.
echo 🔧 للحصول على البيانات الفعلية:
echo    • تأكد من إعداد API_ID و API_HASH في config.py
echo    • تثبيت pyrogram: pip install pyrogram
echo.
echo 🧪 اختبارات متوفرة:
echo    • python test_real_data_system.py
echo    • python test_improved_system.py
echo.
echo 🚀 بدء تشغيل البوت...
echo.

python final_working_bot.py

echo.
echo ⏹️ تم إيقاف البوت
pause
