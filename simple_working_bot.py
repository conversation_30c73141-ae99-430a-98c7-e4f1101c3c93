#!/usr/bin/env python3
"""
بوت بسيط يعمل بشكل مضمون
"""

import logging
import re
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, BotCommand
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# الإعدادات
BOT_TOKEN = "8415486238:AAFpoUZq_OXzZh74o8GrHmXXI7LwqPikSxI"

# تخزين بيانات المستخدمين
user_data = {}

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج أمر /start"""
    user_id = update.effective_user.id
    user_data[user_id] = {"state": "idle"}
    
    keyboard = [
        [InlineKeyboardButton("📊 تقرير عرض النشر اليومي", callback_data='daily_report')],
        [InlineKeyboardButton("📢 تعميم آخر نشر", callback_data='latest_post')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await update.message.reply_text(
        "🤖 مرحباً بك في بوت تحليل المنشورات!\n\nاختر أحد الخيارات التالية:",
        reply_markup=reply_markup
    )

async def button_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج الأزرار"""
    query = update.callback_query
    await query.answer()
    
    user_id = query.from_user.id
    
    if query.data == 'daily_report':
        user_data[user_id] = {"state": "waiting_first_url"}
        
        await query.edit_message_text(
            "📊 تقرير عرض النشر اليومي\n\n"
            "الرجاء إرسال رابط أول منشور:\n"
            "(مثال: https://t.me/channel/123)\n\n"
            "أو أرسل /cancel للإلغاء"
        )
    
    elif query.data == 'latest_post':
        await query.edit_message_text(
            "📢 تعميم آخر نشر\n\n"
            "هذه الميزة قيد التطوير...\n\n"
            "أرسل /start للعودة للقائمة الرئيسية"
        )
    
    elif query.data == 'start_analysis':
        await start_analysis(update, context)

def extract_telegram_info(url: str):
    """استخراج معلومات من رابط تيليجرام"""
    patterns = [
        r't\.me/([^/]+)/(\d+)',
        r'telegram\.me/([^/]+)/(\d+)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return {
                'channel': match.group(1),
                'message_id': int(match.group(2))
            }
    return None

async def handle_text_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج الرسائل النصية"""
    user_id = update.effective_user.id
    text = update.message.text.strip()
    
    print(f"📨 رسالة من المستخدم {user_id}: {text}")
    
    if user_id not in user_data:
        await update.message.reply_text("أرسل /start للبدء")
        return
    
    state = user_data[user_id].get("state", "idle")
    print(f"🔄 حالة المستخدم: {state}")
    
    if state == "waiting_first_url":
        await handle_first_url(update, context)
    elif state == "waiting_last_url":
        await handle_last_url(update, context)
    else:
        await update.message.reply_text("أرسل /start للبدء")

async def handle_first_url(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج رابط أول منشور"""
    user_id = update.effective_user.id
    url = update.message.text.strip()
    
    print(f"🔗 معالجة الرابط الأول: {url}")
    
    # التحقق من صحة الرابط
    if not url.startswith(('http://', 'https://')):
        await update.message.reply_text(
            "❌ الرجاء إرسال رابط صحيح يبدأ بـ http:// أو https://\n"
            "مثال: https://t.me/channel/123"
        )
        return
    
    # التحقق من أنه رابط تيليجرام
    if 't.me' not in url and 'telegram.me' not in url:
        await update.message.reply_text(
            "❌ الرجاء إرسال رابط من تيليجرام\n"
            "مثال: https://t.me/channel/123"
        )
        return
    
    # استخراج معلومات الرابط
    info = extract_telegram_info(url)
    if not info:
        await update.message.reply_text(
            "❌ رابط غير صحيح. تأكد من أن الرابط بالشكل:\n"
            "https://t.me/channel_name/message_id"
        )
        return
    
    # حفظ البيانات
    user_data[user_id].update({
        "state": "waiting_last_url",
        "first_url": url,
        "first_channel": info['channel'],
        "first_message_id": info['message_id']
    })
    
    await update.message.reply_text(
        f"✅ تم حفظ رابط أول منشور\n"
        f"📍 القناة: @{info['channel']}\n"
        f"📝 رقم المنشور: {info['message_id']}\n\n"
        f"الآن الرجاء إرسال رابط آخر منشور من نفس القناة:\n"
        f"(مثال: https://t.me/{info['channel']}/456)"
    )

async def handle_last_url(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج رابط آخر منشور"""
    user_id = update.effective_user.id
    url = update.message.text.strip()
    
    print(f"🔗 معالجة الرابط الأخير: {url}")
    
    # التحقق من صحة الرابط
    if not url.startswith(('http://', 'https://')):
        await update.message.reply_text(
            "❌ الرجاء إرسال رابط صحيح يبدأ بـ http:// أو https://\n"
            "مثال: https://t.me/channel/456"
        )
        return
    
    # التحقق من أنه رابط تيليجرام
    if 't.me' not in url and 'telegram.me' not in url:
        await update.message.reply_text(
            "❌ الرجاء إرسال رابط من تيليجرام\n"
            "مثال: https://t.me/channel/456"
        )
        return
    
    # استخراج معلومات الرابط
    info = extract_telegram_info(url)
    if not info:
        await update.message.reply_text(
            "❌ رابط غير صحيح. تأكد من أن الرابط بالشكل:\n"
            "https://t.me/channel_name/message_id"
        )
        return
    
    # التحقق من أن الرابط من نفس القناة
    first_channel = user_data[user_id]['first_channel']
    if info['channel'] != first_channel:
        await update.message.reply_text(
            f"❌ يجب أن يكون الرابط من نفس القناة: @{first_channel}\n"
            f"الرابط المرسل من قناة: @{info['channel']}\n\n"
            f"الرجاء إرسال رابط من القناة الصحيحة:\n"
            f"https://t.me/{first_channel}/message_id"
        )
        return
    
    # حساب عدد المنشورات
    first_id = user_data[user_id]['first_message_id']
    last_id = info['message_id']
    start_id = min(first_id, last_id)
    end_id = max(first_id, last_id)
    total_posts = end_id - start_id + 1
    
    # حفظ البيانات
    user_data[user_id].update({
        "state": "ready",
        "last_url": url,
        "last_channel": info['channel'],
        "last_message_id": info['message_id'],
        "start_id": start_id,
        "end_id": end_id,
        "total_posts": total_posts
    })
    
    # إنشاء زر التحليل
    keyboard = [[InlineKeyboardButton("🚀 ابدأ الحساب", callback_data='start_analysis')]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await update.message.reply_text(
        f"✅ تم حفظ رابط آخر منشور\n\n"
        f"📊 معلومات التحليل:\n"
        f"📍 القناة: @{info['channel']}\n"
        f"📝 من المنشور: {start_id}\n"
        f"📝 إلى المنشور: {end_id}\n"
        f"🔢 إجمالي المنشورات: {total_posts} منشور\n\n"
        f"اضغط على الزر أدناه لبدء عملية التحليل:",
        reply_markup=reply_markup
    )

async def start_analysis(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """بدء التحليل"""
    query = update.callback_query
    await query.answer()
    
    user_id = query.from_user.id
    
    if user_id not in user_data or user_data[user_id].get("state") != "ready":
        await query.edit_message_text("❌ خطأ: البيانات غير مكتملة")
        return
    
    data = user_data[user_id]
    
    await query.edit_message_text(
        f"🔄 جاري تحليل {data['total_posts']} منشور من قناة @{data['first_channel']}...\n"
        f"📝 من المنشور {data['start_id']} إلى {data['end_id']}\n\n"
        f"الرجاء الانتظار..."
    )
    
    # محاكاة التحليل (يمكن استبدالها بالتحليل الحقيقي)
    import asyncio
    await asyncio.sleep(2)
    
    # تقرير مبسط
    report = f"""📊 تقرير عرض النشر اليومي

📈 إحصائيات عامة:
• القناة: @{data['first_channel']}
• النطاق: من المنشور {data['start_id']} إلى {data['end_id']}
• إجمالي المنشورات: {data['total_posts']} منشور

✅ تم إنشاء التقرير بنجاح

📋 ملاحظة: هذا تقرير تجريبي. للحصول على التحليل الكامل، 
تأكد من تثبيت جميع المكتبات المطلوبة."""
    
    await query.edit_message_text(report)
    
    # إعادة تعيين البيانات
    user_data[user_id] = {"state": "idle"}
    
    # زر العودة
    keyboard = [[InlineKeyboardButton("🔙 بدء تحليل جديد", callback_data='daily_report')]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    await context.bot.send_message(
        chat_id=query.message.chat_id,
        text="أرسل /start لبدء تحليل جديد",
        reply_markup=reply_markup
    )

async def cancel(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """إلغاء العملية"""
    user_id = update.effective_user.id
    user_data[user_id] = {"state": "idle"}
    
    await update.message.reply_text(
        "تم إلغاء العملية.\n\nأرسل /start للبدء من جديد."
    )

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالج أمر /help"""
    help_text = """🤖 بوت تحليل المنشورات

الأوامر المتاحة:
/start - بدء البوت
/help - عرض المساعدة
/cancel - إلغاء العملية

كيفية الاستخدام:
1. أرسل /start
2. اختر "تقرير عرض النشر اليومي"
3. أرسل رابط أول منشور
4. أرسل رابط آخر منشور
5. اضغط "ابدأ الحساب"

مثال على الروابط:
https://t.me/telegram/1
https://t.me/telegram/10"""
    
    await update.message.reply_text(help_text)

async def set_commands(application):
    """إعداد قائمة الأوامر"""
    commands = [
        BotCommand("start", "بدء البوت"),
        BotCommand("help", "المساعدة"),
        BotCommand("cancel", "إلغاء العملية"),
    ]
    
    try:
        await application.bot.set_my_commands(commands)
        print("✅ تم إعداد قائمة الأوامر")
    except Exception as e:
        print(f"⚠️ فشل في إعداد الأوامر: {e}")

def main():
    """تشغيل البوت"""
    print("🤖 بدء تشغيل البوت البسيط...")
    print(f"🔑 رمز البوت: {BOT_TOKEN[:10]}...")
    
    try:
        # إنشاء التطبيق
        application = Application.builder().token(BOT_TOKEN).build()
        
        # إضافة المعالجات
        application.add_handler(CommandHandler('start', start))
        application.add_handler(CommandHandler('help', help_command))
        application.add_handler(CommandHandler('cancel', cancel))
        application.add_handler(CallbackQueryHandler(button_callback))
        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_text_message))
        
        # إعداد الأوامر
        async def post_init(app):
            await set_commands(app)
        
        application.post_init = post_init
        
        print("✅ تم إعداد جميع المعالجات")
        print("🚀 البوت يعمل الآن...")
        print("💡 ابحث عن البوت في تيليجرام وأرسل /start")
        print("💡 اضغط Ctrl+C لإيقاف البوت")
        print("-" * 50)
        
        # تشغيل البوت
        application.run_polling(drop_pending_updates=True)
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف البوت")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
