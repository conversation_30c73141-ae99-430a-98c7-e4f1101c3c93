#!/usr/bin/env python3
"""
اختبار البوت المضمون
"""

import asyncio

def test_imports():
    """اختبار استيراد المكتبات"""
    print("📚 اختبار المكتبات...")
    
    try:
        from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, BotCommand
        from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes
        print("✅ مكتبة python-telegram-bot")
    except ImportError as e:
        print(f"❌ مكتبة python-telegram-bot: {e}")
        return False
    
    try:
        from config import BOT_TOKEN, SAVED_POSTS, SIMILARITY_THRESHOLD, API_ID, API_HASH
        print("✅ ملف config.py")
    except ImportError as e:
        print(f"❌ ملف config.py: {e}")
        return False
    
    try:
        from utils import check_post_similarity
        print("✅ ملف utils.py")
    except ImportError as e:
        print(f"❌ ملف utils.py: {e}")
        return False
    
    try:
        from telegram_reader import analyze_telegram_posts, TelegramReader
        print("✅ ملف telegram_reader.py")
    except ImportError as e:
        print(f"❌ ملف telegram_reader.py: {e}")
        return False
    
    return True

def test_config():
    """اختبار الإعدادات"""
    print("\n⚙️ اختبار الإعدادات...")
    
    try:
        from config import BOT_TOKEN, API_ID, API_HASH, SAVED_POSTS, SIMILARITY_THRESHOLD
        
        if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
            print("❌ BOT_TOKEN غير معد")
            return False
        else:
            print(f"✅ BOT_TOKEN: {BOT_TOKEN[:10]}...")
        
        if API_ID == "YOUR_API_ID_HERE":
            print("❌ API_ID غير معد")
            return False
        else:
            print(f"✅ API_ID: {API_ID}")
        
        if API_HASH == "YOUR_API_HASH_HERE":
            print("❌ API_HASH غير معد")
            return False
        else:
            print(f"✅ API_HASH: {API_HASH[:10]}...")
        
        print(f"✅ عدد المنشورات المحفوظة: {len(SAVED_POSTS)}")
        print(f"✅ نسبة التشابه: {SIMILARITY_THRESHOLD}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإعدادات: {e}")
        return False

async def test_bot_creation():
    """اختبار إنشاء البوت"""
    print("\n🤖 اختبار إنشاء البوت...")
    
    try:
        from telegram.ext import Application
        from config import BOT_TOKEN
        
        application = Application.builder().token(BOT_TOKEN).build()
        print("✅ تم إنشاء تطبيق البوت")
        
        # اختبار الاتصال
        bot = application.bot
        me = await bot.get_me()
        print(f"✅ معلومات البوت: {me.first_name} (@{me.username})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البوت: {e}")
        return False

def test_url_extraction():
    """اختبار استخراج معلومات الروابط"""
    print("\n🔗 اختبار استخراج الروابط...")
    
    try:
        from telegram_reader import TelegramReader
        
        reader = TelegramReader()
        
        test_urls = [
            "https://t.me/telegram/1",
            "https://t.me/durov/123",
            "https://telegram.me/channel/456"
        ]
        
        for url in test_urls:
            info = reader.extract_channel_info(url)
            if info:
                print(f"✅ {url} -> @{info['channel']}/{info['message_id']}")
            else:
                print(f"❌ فشل في تحليل: {url}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استخراج الروابط: {e}")
        return False

def test_user_session():
    """اختبار جلسة المستخدم"""
    print("\n👤 اختبار جلسة المستخدم...")
    
    try:
        # محاكاة جلسة مستخدم
        class UserSession:
            def __init__(self):
                self.state = "IDLE"
                self.first_url = None
                self.last_url = None
                self.first_channel = None
                self.first_message_id = None
                self.total_posts = 0
        
        session = UserSession()
        session.state = "WAITING_FIRST_URL"
        session.first_url = "https://t.me/telegram/1"
        session.first_channel = "telegram"
        session.first_message_id = 1
        
        print("✅ إنشاء جلسة المستخدم")
        
        # محاكاة حساب العدد
        last_message_id = 10
        total_posts = last_message_id - session.first_message_id + 1
        session.total_posts = total_posts
        
        print(f"✅ حساب العدد: {total_posts} منشور")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في جلسة المستخدم: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار البوت المضمون")
    print("=" * 40)
    
    success = True
    
    # اختبار المكتبات
    if not test_imports():
        success = False
    
    # اختبار الإعدادات
    if not test_config():
        success = False
    
    # اختبار إنشاء البوت
    if not await test_bot_creation():
        success = False
    
    # اختبار استخراج الروابط
    if not test_url_extraction():
        success = False
    
    # اختبار جلسة المستخدم
    if not test_user_session():
        success = False
    
    print("\n" + "=" * 40)
    
    if success:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n📋 الخطوات التالية:")
        print("1. شغل البوت: python working_telegram_bot.py")
        print("2. أو استخدم: run_working_bot.bat")
        print("3. ابحث عن البوت في تيليجرام")
        print("4. ستجد /start في قائمة الأوامر أسفل الشات")
        print("5. أرسل /start وجرب البوت")
        
        print("\n💡 مثال للاختبار:")
        print("- الرابط الأول: https://t.me/telegram/1")
        print("- الرابط الأخير: https://t.me/telegram/5")
        print("- النتيجة المتوقعة: 5 منشورات")
        
        print("\n✨ المميزات الجديدة:")
        print("- ✅ حساب عدد المنشورات تلقائياً")
        print("- ✅ قائمة أوامر في الشات")
        print("- ✅ معالجة محسنة للرسائل")
        print("- ✅ تقارير مفصلة")
        
    else:
        print("❌ بعض الاختبارات فشلت")
        print("\n💡 نصائح:")
        print("- تأكد من تثبيت جميع المكتبات")
        print("- تحقق من API_ID و API_HASH")
        print("- تأكد من اتصال الإنترنت")
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if not result:
            print("\n🔧 للمساعدة:")
            print("- شغل: python test_api.py")
            print("- راجع: HOW_TO_GET_API_CREDENTIALS.md")
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
    
    input("\nاضغط Enter للخروج...")
