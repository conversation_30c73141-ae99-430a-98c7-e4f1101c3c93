@echo off
chcp 65001 > nul
echo 🔍 تشخيص مشاكل البوت
echo ========================
echo.

REM اختبار Python
echo 1️⃣ اختبار Python...
python --version
if errorlevel 1 (
    echo ❌ Python غير متاح
    echo 💡 قم بتثبيت Python من https://python.org
    pause
    exit /b 1
) else (
    echo ✅ Python متاح
)
echo.

REM اختبار pip
echo 2️⃣ اختبار pip...
python -m pip --version
if errorlevel 1 (
    echo ❌ pip غير متاح
    pause
    exit /b 1
) else (
    echo ✅ pip متاح
)
echo.

REM اختبار المكتبات
echo 3️⃣ اختبار المكتبات...
python -c "import telegram; print('✅ python-telegram-bot متاح')" 2>nul
if errorlevel 1 (
    echo ❌ python-telegram-bot غير متاح
    echo 💡 تثبيت المكتبة...
    python -m pip install python-telegram-bot
)

python -c "import requests; print('✅ requests متاح')" 2>nul
if errorlevel 1 (
    echo ❌ requests غير متاح
    echo 💡 تثبيت المكتبة...
    python -m pip install requests
)

python -c "import bs4; print('✅ beautifulsoup4 متاح')" 2>nul
if errorlevel 1 (
    echo ❌ beautifulsoup4 غير متاح
    echo 💡 تثبيت المكتبة...
    python -m pip install beautifulsoup4
)

python -c "import dateutil; print('✅ python-dateutil متاح')" 2>nul
if errorlevel 1 (
    echo ❌ python-dateutil غير متاح
    echo 💡 تثبيت المكتبة...
    python -m pip install python-dateutil
)
echo.

REM اختبار ملف الإعدادات
echo 4️⃣ اختبار ملف الإعدادات...
if not exist config.py (
    echo ❌ ملف config.py غير موجود
    pause
    exit /b 1
)

python -c "from config import BOT_TOKEN; print('✅ رمز البوت متاح' if BOT_TOKEN != 'YOUR_BOT_TOKEN_HERE' else '❌ رمز البوت غير معد')"
echo.

REM تشغيل التشخيص المتقدم
echo 5️⃣ تشغيل التشخيص المتقدم...
python debug_bot.py
echo.

echo 🎯 خيارات التشغيل:
echo.
echo 1. تشغيل البوت الكامل: python bot.py
echo 2. تشغيل البوت المبسط: python minimal_bot.py
echo 3. إعادة التشخيص: diagnose.bat
echo.

set /p choice="اختر رقم (1-3) أو اضغط Enter للخروج: "

if "%choice%"=="1" (
    echo.
    echo 🚀 تشغيل البوت الكامل...
    python bot.py
) else if "%choice%"=="2" (
    echo.
    echo 🚀 تشغيل البوت المبسط...
    python minimal_bot.py
) else if "%choice%"=="3" (
    echo.
    diagnose.bat
) else (
    echo 👋 وداعاً!
)

pause
